# Multi-stage build for Clojure application
FROM clojure:temurin-21-tools-deps-alpine AS builder

# Set working directory
WORKDIR /app

# Copy dependency files
COPY deps.edn .
COPY build.clj .

# Download dependencies (this layer will be cached if deps.edn doesn't change)
RUN clj -P

# Copy source code
COPY src/ src/
COPY resources/ resources/

# Build uberjar
RUN clj -T:build uberjar

# Production stage
FROM eclipse-temurin:21-jre-alpine

# Install curl for healthchecks
RUN apk add --no-cache curl

# Create app user
RUN addgroup -g 1001 -S appgroup && \
    adduser -u 1001 -S appuser -G appgroup

# Set working directory
WORKDIR /app

# Copy the uberjar from builder stage
COPY --from=builder /app/target/trade-finance-standalone.jar app.jar

# Change ownership to app user
RUN chown -R appuser:appgroup /app

# Switch to app user
USER appuser

# Expose port
EXPOSE 8090

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8090/health || exit 1

# Run the application
CMD ["java", "-jar", "app.jar"]
