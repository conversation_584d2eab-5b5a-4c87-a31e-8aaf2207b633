Of course. The reason your file couldn't be opened was the complete lack of visual information. To fix this, I have generated the necessary BPMN Diagram Interchange (BPMNDI) data which describes a logical layout for your process.

I have laid out the process flow from left to right, with rejection and exception paths branching off below the main flow.

Below is the complete, corrected BPMN code. You can copy this entire block and save it as a `.bpmn` file, and it should now open correctly in any standard BPMN modeling tool.

-----

```xml
<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL"
                  xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI"
                  xmlns:dc="http://www.omg.org/spec/DD/20100524/DC"
                  xmlns:di="http://www.omg.org/spec/DD/20100524/DI"
                  xmlns:camunda="http://camunda.org/schema/1.0/bpmn"
                  id="Definitions_1"
                  targetNamespace="http://bpmn.io/schema/bpmn">
  
  <bpmn:process id="invoice-discounting-process" name="Invoice Discounting Process" isExecutable="true">
    
    <bpmn:startEvent id="StartEvent_InvoiceSubmission" name="Invoice Discounting Request">
      <bpmn:outgoing>SequenceFlow_1</bpmn:outgoing>
    </bpmn:startEvent>
    
    <bpmn:userTask id="Task_ValidateInvoice" name="Validate Invoice Details" camunda:formKey="validate-invoice-form">
      <bpmn:incoming>SequenceFlow_1</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_2</bpmn:outgoing>
    </bpmn:userTask>
    
    <bpmn:serviceTask id="Task_CheckEligibility" name="Check Client Eligibility" camunda:class="com.bank.CheckEligibilityDelegate">
      <bpmn:incoming>SequenceFlow_2</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_3</bpmn:outgoing>
    </bpmn:serviceTask>
    
    <bpmn:exclusiveGateway id="Gateway_EligibilityCheck" name="Client Eligible?">
      <bpmn:incoming>SequenceFlow_3</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_Eligible</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_NotEligible</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    
    <bpmn:serviceTask id="Task_VerifyDebtor" name="Verify Debtor Information" camunda:class="com.bank.VerifyDebtorDelegate">
      <bpmn:incoming>SequenceFlow_Eligible</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_4</bpmn:outgoing>
    </bpmn:serviceTask>
    
    <bpmn:userTask id="Task_CreditAssessment" name="Credit Risk Assessment" camunda:formKey="credit-assessment-form">
      <bpmn:incoming>SequenceFlow_4</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_5</bpmn:outgoing>
    </bpmn:userTask>
    
    <bpmn:exclusiveGateway id="Gateway_RiskDecision" name="Risk Acceptable?">
      <bpmn:incoming>SequenceFlow_5</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_Approved</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_Rejected</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    
    <bpmn:serviceTask id="Task_CalculateDiscount" name="Calculate Discount Rate" camunda:class="com.bank.CalculateDiscountDelegate">
      <bpmn:incoming>SequenceFlow_Approved</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_6</bpmn:outgoing>
    </bpmn:serviceTask>
    
    <bpmn:serviceTask id="Task_GenerateAgreement" name="Generate Facility Agreement" camunda:class="com.bank.GenerateAgreementDelegate">
      <bpmn:incoming>SequenceFlow_6</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_7</bpmn:outgoing>
    </bpmn:serviceTask>
    
    <bpmn:userTask id="Task_ClientAgreement" name="Client Agreement Review" camunda:formKey="client-agreement-form">
      <bpmn:incoming>SequenceFlow_7</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_8</bpmn:outgoing>
    </bpmn:userTask>
    
    <bpmn:exclusiveGateway id="Gateway_AgreementSigned" name="Agreement Signed?">
      <bpmn:incoming>SequenceFlow_8</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_Signed</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_NotSigned</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    
    <bpmn:parallelGateway id="Gateway_ParallelStart" name="Parallel Processing">
      <bpmn:incoming>SequenceFlow_Signed</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_9a</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_9b</bpmn:outgoing>
    </bpmn:parallelGateway>
    
    <bpmn:serviceTask id="Task_DisburseFunds" name="Disburse Funds" camunda:class="com.bank.DisburseFundsDelegate">
      <bpmn:incoming>SequenceFlow_9a</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_10a</bpmn:outgoing>
    </bpmn:serviceTask>
    
    <bpmn:serviceTask id="Task_SetupCollection" name="Set Up Collection Process" camunda:class="com.bank.SetupCollectionDelegate">
      <bpmn:incoming>SequenceFlow_9b</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_10b</bpmn:outgoing>
    </bpmn:serviceTask>
    
    <bpmn:parallelGateway id="Gateway_ParallelEnd" name="Parallel End">
      <bpmn:incoming>SequenceFlow_10a</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_10b</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_11</bpmn:outgoing>
    </bpmn:parallelGateway>
    
    <bpmn:serviceTask id="Task_MonitorCollections" name="Monitor Collections" camunda:class="com.bank.MonitorCollectionsDelegate">
      <bpmn:incoming>SequenceFlow_11</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_12</bpmn:outgoing>
    </bpmn:serviceTask>
    
    <bpmn:exclusiveGateway id="Gateway_PaymentReceived" name="Payment Received?">
      <bpmn:incoming>SequenceFlow_12</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_PaymentReceived</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_PaymentOverdue</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    
    <bpmn:serviceTask id="Task_ProcessSettlement" name="Process Settlement" camunda:class="com.bank.ProcessSettlementDelegate">
      <bpmn:incoming>SequenceFlow_PaymentReceived</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_13</bpmn:outgoing>
    </bpmn:serviceTask>
    
    <bpmn:userTask id="Task_HandleDefault" name="Handle Default" camunda:formKey="handle-default-form">
      <bpmn:incoming>SequenceFlow_PaymentOverdue</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_14</bpmn:outgoing>
    </bpmn:userTask>
    
    <bpmn:serviceTask id="Task_NotifyRejection" name="Notify Rejection" camunda:class="com.bank.NotifyRejectionDelegate">
      <bpmn:incoming>SequenceFlow_NotEligible</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_Rejected</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_NotSigned</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_15</bpmn:outgoing>
    </bpmn:serviceTask>
    
    <bpmn:endEvent id="EndEvent_Success" name="Process Completed Successfully">
      <bpmn:incoming>SequenceFlow_13</bpmn:incoming>
    </bpmn:endEvent>
    
    <bpmn:endEvent id="EndEvent_Default" name="Default Handled">
      <bpmn:incoming>SequenceFlow_14</bpmn:incoming>
    </bpmn:endEvent>
    
    <bpmn:endEvent id="EndEvent_Rejection" name="Application Rejected">
      <bpmn:incoming>SequenceFlow_15</bpmn:incoming>
    </bpmn:endEvent>
    
    <bpmn:sequenceFlow id="SequenceFlow_1" sourceRef="StartEvent_InvoiceSubmission" targetRef="Task_ValidateInvoice" />
    <bpmn:sequenceFlow id="SequenceFlow_2" sourceRef="Task_ValidateInvoice" targetRef="Task_CheckEligibility" />
    <bpmn:sequenceFlow id="SequenceFlow_3" sourceRef="Task_CheckEligibility" targetRef="Gateway_EligibilityCheck" />
    <bpmn:sequenceFlow id="SequenceFlow_Eligible" name="Yes" sourceRef="Gateway_EligibilityCheck" targetRef="Task_VerifyDebtor">
      <bpmn:conditionExpression>${eligible == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_NotEligible" name="No" sourceRef="Gateway_EligibilityCheck" targetRef="Task_NotifyRejection">
      <bpmn:conditionExpression>${eligible == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_4" sourceRef="Task_VerifyDebtor" targetRef="Task_CreditAssessment" />
    <bpmn:sequenceFlow id="SequenceFlow_5" sourceRef="Task_CreditAssessment" targetRef="Gateway_RiskDecision" />
    <bpmn:sequenceFlow id="SequenceFlow_Approved" name="Yes" sourceRef="Gateway_RiskDecision" targetRef="Task_CalculateDiscount">
      <bpmn:conditionExpression>${riskAcceptable == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_Rejected" name="No" sourceRef="Gateway_RiskDecision" targetRef="Task_NotifyRejection">
      <bpmn:conditionExpression>${riskAcceptable == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_6" sourceRef="Task_CalculateDiscount" targetRef="Task_GenerateAgreement" />
    <bpmn:sequenceFlow id="SequenceFlow_7" sourceRef="Task_GenerateAgreement" targetRef="Task_ClientAgreement" />
    <bpmn:sequenceFlow id="SequenceFlow_8" sourceRef="Task_ClientAgreement" targetRef="Gateway_AgreementSigned" />
    <bpmn:sequenceFlow id="SequenceFlow_Signed" name="Yes" sourceRef="Gateway_AgreementSigned" targetRef="Gateway_ParallelStart">
      <bpmn:conditionExpression>${agreementSigned == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_NotSigned" name="No" sourceRef="Gateway_AgreementSigned" targetRef="Task_NotifyRejection">
      <bpmn:conditionExpression>${agreementSigned == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_9a" sourceRef="Gateway_ParallelStart" targetRef="Task_DisburseFunds" />
    <bpmn:sequenceFlow id="SequenceFlow_9b" sourceRef="Gateway_ParallelStart" targetRef="Task_SetupCollection" />
    <bpmn:sequenceFlow id="SequenceFlow_10a" sourceRef="Task_DisburseFunds" targetRef="Gateway_ParallelEnd" />
    <bpmn:sequenceFlow id="SequenceFlow_10b" sourceRef="Task_SetupCollection" targetRef="Gateway_ParallelEnd" />
    <bpmn:sequenceFlow id="SequenceFlow_11" sourceRef="Gateway_ParallelEnd" targetRef="Task_MonitorCollections" />
    <bpmn:sequenceFlow id="SequenceFlow_12" sourceRef="Task_MonitorCollections" targetRef="Gateway_PaymentReceived" />
    <bpmn:sequenceFlow id="SequenceFlow_PaymentReceived" name="Yes" sourceRef="Gateway_PaymentReceived" targetRef="Task_ProcessSettlement">
      <bpmn:conditionExpression>${paymentReceived == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_PaymentOverdue" name="No" sourceRef="Gateway_PaymentReceived" targetRef="Task_HandleDefault">
      <bpmn:conditionExpression>${paymentReceived == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_13" sourceRef="Task_ProcessSettlement" targetRef="EndEvent_Success" />
    <bpmn:sequenceFlow id="SequenceFlow_14" sourceRef="Task_HandleDefault" targetRef="EndEvent_Default" />
    <bpmn:sequenceFlow id="SequenceFlow_15" sourceRef="Task_NotifyRejection" targetRef="EndEvent_Rejection" />
    
  </bpmn:process>
  
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="invoice-discounting-process">
      <bpmndi:BPMNShape id="StartEvent_InvoiceSubmission_di" bpmnElement="StartEvent_InvoiceSubmission">
        <dc:Bounds x="152" y="102" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="127" y="145" width="87" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_ValidateInvoice_di" bpmnElement="Task_ValidateInvoice">
        <dc:Bounds x="240" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_CheckEligibility_di" bpmnElement="Task_CheckEligibility">
        <dc:Bounds x="390" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_EligibilityCheck_di" bpmnElement="Gateway_EligibilityCheck" isMarkerVisible="true">
        <dc:Bounds x="545" y="95" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="532" y="65" width="77" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_VerifyDebtor_di" bpmnElement="Task_VerifyDebtor">
        <dc:Bounds x="650" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_CreditAssessment_di" bpmnElement="Task_CreditAssessment">
        <dc:Bounds x="800" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_RiskDecision_di" bpmnElement="Gateway_RiskDecision" isMarkerVisible="true">
        <dc:Bounds x="955" y="95" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="935" y="65" width="90" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_CalculateDiscount_di" bpmnElement="Task_CalculateDiscount">
        <dc:Bounds x="1060" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_GenerateAgreement_di" bpmnElement="Task_GenerateAgreement">
        <dc:Bounds x="1210" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_ClientAgreement_di" bpmnElement="Task_ClientAgreement">
        <dc:Bounds x="1360" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_AgreementSigned_di" bpmnElement="Gateway_AgreementSigned" isMarkerVisible="true">
        <dc:Bounds x="1515" y="95" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1500" y="65" width="81" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_ParallelStart_di" bpmnElement="Gateway_ParallelStart">
        <dc:Bounds x="1625" y="95" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1607" y="152" width="87" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_DisburseFunds_di" bpmnElement="Task_DisburseFunds">
        <dc:Bounds x="1730" y="40" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_SetupCollection_di" bpmnElement="Task_SetupCollection">
        <dc:Bounds x="1730" y="150" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_ParallelEnd_di" bpmnElement="Gateway_ParallelEnd">
        <dc:Bounds x="1885" y="95" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1878" y="152" width="65" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_MonitorCollections_di" bpmnElement="Task_MonitorCollections">
        <dc:Bounds x="1990" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_PaymentReceived_di" bpmnElement="Gateway_PaymentReceived" isMarkerVisible="true">
        <dc:Bounds x="2145" y="95" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2126" y="65" width="89" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_ProcessSettlement_di" bpmnElement="Task_ProcessSettlement">
        <dc:Bounds x="2250" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_Success_di" bpmnElement="EndEvent_Success">
        <dc:Bounds x="2402" y="102" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2377" y="145" width="87" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_HandleDefault_di" bpmnElement="Task_HandleDefault">
        <dc:Bounds x="2250" y="240" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_Default_di" bpmnElement="EndEvent_Default">
        <dc:Bounds x="2402" y="262" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2382" y="305" width="77" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_NotifyRejection_di" bpmnElement="Task_NotifyRejection">
        <dc:Bounds x="930" y="360" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_Rejection_di" bpmnElement="EndEvent_Rejection">
        <dc:Bounds x="1082" y="382" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1058" y="425" width="84" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      
      <bpmndi:BPMNEdge id="SequenceFlow_1_di" bpmnElement="SequenceFlow_1">
        <di:waypoint x="188" y="120" />
        <di:waypoint x="240" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_2_di" bpmnElement="SequenceFlow_2">
        <di:waypoint x="340" y="120" />
        <di:waypoint x="390" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_3_di" bpmnElement="SequenceFlow_3">
        <di:waypoint x="490" y="120" />
        <di:waypoint x="545" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_Eligible_di" bpmnElement="SequenceFlow_Eligible">
        <di:waypoint x="595" y="120" />
        <di:waypoint x="650" y="120" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="614" y="102" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_4_di" bpmnElement="SequenceFlow_4">
        <di:waypoint x="750" y="120" />
        <di:waypoint x="800" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_5_di" bpmnElement="SequenceFlow_5">
        <di:waypoint x="900" y="120" />
        <di:waypoint x="955" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_Approved_di" bpmnElement="SequenceFlow_Approved">
        <di:waypoint x="1005" y="120" />
        <di:waypoint x="1060" y="120" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1024" y="102" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_6_di" bpmnElement="SequenceFlow_6">
        <di:waypoint x="1160" y="120" />
        <di:waypoint x="1210" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_7_di" bpmnElement="SequenceFlow_7">
        <di:waypoint x="1310" y="120" />
        <di:waypoint x="1360" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_8_di" bpmnElement="SequenceFlow_8">
        <di:waypoint x="1460" y="120" />
        <di:waypoint x="1515" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_Signed_di" bpmnElement="SequenceFlow_Signed">
        <di:waypoint x="1565" y="120" />
        <di:waypoint x="1625" y="120" />
         <bpmndi:BPMNLabel>
          <dc:Bounds x="1586" y="102" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_9a_di" bpmnElement="SequenceFlow_9a">
        <di:waypoint x="1650" y="95" />
        <di:waypoint x="1650" y="80" />
        <di:waypoint x="1730" y="80" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_9b_di" bpmnElement="SequenceFlow_9b">
        <di:waypoint x="1650" y="145" />
        <di:waypoint x="1650" y="190" />
        <di:waypoint x="1730" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_10a_di" bpmnElement="SequenceFlow_10a">
        <di:waypoint x="1830" y="80" />
        <di:waypoint x="1910" y="80" />
        <di:waypoint x="1910" y="95" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_10b_di" bpmnElement="SequenceFlow_10b">
        <di:waypoint x="1830" y="190" />
        <di:waypoint x="1910" y="190" />
        <di:waypoint x="1910" y="145" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_11_di" bpmnElement="SequenceFlow_11">
        <di:waypoint x="1935" y="120" />
        <di:waypoint x="1990" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_12_di" bpmnElement="SequenceFlow_12">
        <di:waypoint x="2090" y="120" />
        <di:waypoint x="2145" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_PaymentReceived_di" bpmnElement="SequenceFlow_PaymentReceived">
        <di:waypoint x="2195" y="120" />
        <di:waypoint x="2250" y="120" />
         <bpmndi:BPMNLabel>
          <dc:Bounds x="2214" y="102" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_13_di" bpmnElement="SequenceFlow_13">
        <di:waypoint x="2350" y="120" />
        <di:waypoint x="2402" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_PaymentOverdue_di" bpmnElement="SequenceFlow_PaymentOverdue">
        <di:waypoint x="2170" y="145" />
        <di:waypoint x="2170" y="280" />
        <di:waypoint x="2250" y="280" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2181" y="209" width="19" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_14_di" bpmnElement="SequenceFlow_14">
        <di:waypoint x="2350" y="280" />
        <di:waypoint x="2402" y="280" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_NotEligible_di" bpmnElement="SequenceFlow_NotEligible">
        <di:waypoint x="570" y="145" />
        <di:waypoint x="570" y="400" />
        <di:waypoint x="930" y="400" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="581" y="269" width="19" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_Rejected_di" bpmnElement="SequenceFlow_Rejected">
        <di:waypoint x="980" y="145" />
        <di:waypoint x="980" y="360" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="989" y="249" width="19" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_NotSigned_di" bpmnElement="SequenceFlow_NotSigned">
        <di:waypoint x="1540" y="145" />
        <di:waypoint x="1540" y="400" />
        <di:waypoint x="1030" y="400" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1277" y="363" width="19" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_15_di" bpmnElement="SequenceFlow_15">
        <di:waypoint x="1030" y="400" />
        <di:waypoint x="1082" y="400" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
```