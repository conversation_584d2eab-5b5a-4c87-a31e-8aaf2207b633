version: '3.8'

services:
  # PostgreSQL database for Flowable
  flowable-db:
    image: postgres:13
    environment:
      POSTGRES_DB: flowable
      POSTGRES_USER: flowable
      POSTGRES_PASSWORD: flowable
    volumes:
      - flowable_db_data:/var/lib/postgresql/data
    ports:
      - "5433:5432"
    networks:
      - flowable-network

  # PostgreSQL database for Trade Finance application
  trade-finance-db:
    image: postgres:13
    environment:
      POSTGRES_DB: trade_finance
      POSTGRES_USER: trade_finance
      POSTGRES_PASSWORD: trade_finance
    volumes:
      - trade_finance_db_data:/var/lib/postgresql/data
    ports:
      - "5434:5432"
    networks:
      - flowable-network

  # Flowable UI and Engine
  flowable-app:
    image: flowable/flowable-ui:6.8.0
    depends_on:
      - flowable-db
    environment:
      - FLOWABLE_DATABASE_URL=*******************************************
      - FLOWABLE_DATABASE_USERNAME=flowable
      - FLOWABLE_DATABASE_PASSWORD=flowable
      - FLOWABLE_DATABASE_DRIVER=org.postgresql.Driver
    ports:
      - "8080:8080"
    networks:
      - flowable-network
    volumes:
      - ./bpmn-models:/opt/flowable/models

  # Trade Finance Clojure Application
  trade-finance-app:
    build: .
    depends_on:
      - trade-finance-db
      - flowable-app
    environment:
      - DB_HOST=trade-finance-db
      - DB_PORT=5432
      - DB_NAME=trade_finance
      - DB_USER=trade_finance
      - DB_PASSWORD=trade_finance
      - FLOWABLE_URL=http://flowable-app:8080/flowable-rest/service
      - FLOWABLE_USER=admin
      - FLOWABLE_PASSWORD=test
      - HOST=0.0.0.0
      - PORT=8090
    ports:
      - "8090:8090"
    networks:
      - flowable-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8090/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s


volumes:
  flowable_db_data:
  trade_finance_db_data:

networks:
  flowable-network:
    driver: bridge
