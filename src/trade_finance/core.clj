(ns trade-finance.core
  (:require [io.pedestal.http :as http]
            [io.pedestal.http.route :as route]
            [io.pedestal.http.body-params :as body-params]
            [io.pedestal.interceptor :as interceptor]
            [next.jdbc :as jdbc]
            [next.jdbc.sql :as sql]

            [clojure.data.json :as json]
            [clojure.tools.logging :as log]
            [java-time.api :as time])
  (:gen-class))

;; Database configuration
(def db-spec {:dbtype "sqlite" :dbname "trade_finance.db"})

;; Flowable REST API configuration
(def flowable-config
  {:base-url "http://localhost:8080/flowable-rest/service"
   :auth {:basic-auth ["admin" "test"]}  ; Default Flowable credentials
   :headers {"Content-Type" "application/json"}})

;; Initialize database schema
(defn init-db! []
  (jdbc/execute! db-spec
    ["CREATE TABLE IF NOT EXISTS invoice_requests (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        process_instance_id TEXT UNIQUE,
        client_id TEXT NOT NULL,
        invoice_number TEXT NOT NULL,
        amount DECIMAL(15,2) NOT NULL,
        debtor_name TEXT NOT NULL,
        status TEXT NOT NULL DEFAULT 'SUBMITTED',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )"])
  
  (jdbc/execute! db-spec
    ["CREATE TABLE IF NOT EXISTS process_events (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        process_instance_id TEXT NOT NULL,
        event_type TEXT NOT NULL,
        event_data TEXT,
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )"])
  
  (log/info "Database initialized"))

;; Business Logic (External Tasks)
(defn check-client-eligibility
  "Simulate eligibility check - in real system this would check credit bureau, limits, etc."
  [client-id amount]
  (let [eligible? (and (> amount 1000) (< amount 1000000))]  ; Simple rules
    (log/info "Checking eligibility for client:" client-id "amount:" amount "eligible:" eligible?)
    {:eligible eligible?
     :reason (if eligible? "Within limits" "Amount outside acceptable range")}))

(defn verify-debtor-info
  "Simulate debtor verification - in real system this would check external databases"
  [debtor-name]
  (let [verified? (> (count debtor-name) 2)]  ; Simple validation
    (log/info "Verifying debtor:" debtor-name "verified:" verified?)
    {:verified verified?
     :credit-score (if verified? (+ 600 (rand-int 200)) 0)}))

(defn calculate-discount-rate
  "Calculate discount rate based on amount and credit score"
  [amount credit-score]
  (let [base-rate 0.05
        amount-factor (/ amount 100000)  ; Higher amount = slightly higher rate
        credit-factor (/ (- 800 credit-score) 1000)  ; Lower credit = higher rate
        rate (+ base-rate amount-factor credit-factor)]
    (log/info "Calculated discount rate:" rate "for amount:" amount "credit:" credit-score)
    {:discount-rate (max 0.02 (min 0.15 rate))}))

;; Database operations
(defn save-invoice-request [invoice-data process-instance-id]
  (sql/insert! db-spec :invoice_requests
    (assoc invoice-data
           :process_instance_id process-instance-id
           :created_at (time/instant)
           :updated_at (time/instant))))

(defn get-invoice-by-process-id [process-instance-id]
  (sql/get-by-id db-spec :invoice_requests process-instance-id "process_instance_id"))

(defn update-invoice-status [process-instance-id status]
  (sql/update! db-spec :invoice_requests
    {:status status :updated_at (time/instant)}
    ["process_instance_id = ?" process-instance-id]))

(defn log-process-event [process-instance-id event-type event-data]
  (sql/insert! db-spec :process_events
    {:process_instance_id process-instance-id
     :event_type event-type
     :event_data (json/write-str event-data)
     :timestamp (time/instant)}))

;; BPMN Process Integration (Simulated)
(defn start-invoice-process
  "Simulates starting a BPMN process - in real implementation this would call Flowable"
  [invoice-data]
  (let [process-id (str "process-" (java.util.UUID/randomUUID))]

    (log/info "Starting simulated BPMN process for invoice:" (:invoice-number invoice-data))

    ;; Simulate the BPMN process steps
    (future
      (Thread/sleep 1000)
      (log/info "BPMN Step 1: Validating invoice details for process" process-id)

      (Thread/sleep 1000)
      (let [eligibility-result (check-client-eligibility (:client-id invoice-data) (:amount invoice-data))]
        (log/info "BPMN Step 2: Client eligibility check result:" eligibility-result)
        (update-invoice-status process-id (if (:eligible eligibility-result) "ELIGIBILITY_PASSED" "ELIGIBILITY_FAILED")))

      (Thread/sleep 1000)
      (let [debtor-result (verify-debtor-info (:debtor-name invoice-data))]
        (log/info "BPMN Step 3: Debtor verification result:" debtor-result)
        (update-invoice-status process-id (if (:verified debtor-result) "DEBTOR_VERIFIED" "DEBTOR_FAILED")))

      (Thread/sleep 1000)
      (let [discount-result (calculate-discount-rate (:amount invoice-data) 750)]
        (log/info "BPMN Step 4: Discount calculation result:" discount-result)
        (update-invoice-status process-id "DISCOUNT_CALCULATED"))

      (Thread/sleep 1000)
      (log/info "BPMN Process completed for process" process-id)
      (update-invoice-status process-id "COMPLETED"))

    ;; Return simulated process response
    {:id process-id
     :processDefinitionKey "invoice-discounting-process"
     :businessKey (:invoice-number invoice-data)
     :suspended false
     :ended false}))

;; API Handlers
(defn submit-invoice-handler [request]
  (let [invoice-data (get-in request [:json-params])
        _ (log/info "Received invoice submission:" invoice-data)
        
        ;; Start BPMN process
        process-result (start-invoice-process invoice-data)]
    
    (if (:error process-result)
      {:status 500
       :body {:error "Failed to start process" :details process-result}}
      
      (let [process-id (:id process-result)]
        ;; Save to database
        (save-invoice-request invoice-data process-id)
        (log-process-event process-id "INVOICE_SUBMITTED" invoice-data)
        
        {:status 200
         :body {:message "Invoice submitted successfully"
                :process-id process-id
                :invoice-number (:invoice-number invoice-data)}}))))

(defn get-invoice-status-handler [request]
  (let [process-id (get-in request [:path-params :process-id])
        invoice (get-invoice-by-process-id process-id)]
    
    (if invoice
      {:status 200
       :body {:invoice invoice
              :process-id process-id}}
      {:status 404
       :body {:error "Invoice not found"}})))

;; External Task Processing (simulates Flowable calling back)
(defn process-eligibility-task [request]
  (let [task-data (get-in request [:json-params])
        client-id (get-in task-data [:variables :client-id :value])
        amount (get-in task-data [:variables :amount :value])
        
        result (check-client-eligibility client-id amount)]
    
    (log/info "Processing eligibility task for client:" client-id)
    
    {:status 200
     :body {:variables {:eligible {:value (:eligible result)}
                        :eligibility-reason {:value (:reason result)}}}}))

(defn process-debtor-verification-task [request]
  (let [task-data (get-in request [:json-params])
        debtor-name (get-in task-data [:variables :debtor-name :value])
        
        result (verify-debtor-info debtor-name)]
    
    (log/info "Processing debtor verification for:" debtor-name)
    
    {:status 200
     :body {:variables {:debtor-verified {:value (:verified result)}
                        :credit-score {:value (:credit-score result)}}}}))

(defn process-discount-calculation-task [request]
  (let [task-data (get-in request [:json-params])
        amount (get-in task-data [:variables :amount :value])
        credit-score (get-in task-data [:variables :credit-score :value])
        
        result (calculate-discount-rate amount credit-score)]
    
    (log/info "Calculating discount rate for amount:" amount "credit:" credit-score)
    
    {:status 200
     :body {:variables {:discount-rate {:value (:discount-rate result)}}}}))

;; Routes
(def routes
  #{["/api/invoice/submit" :post [(body-params/body-params) submit-invoice-handler]]
    ["/api/invoice/status/:process-id" :get get-invoice-status-handler]
    
    ;; External task endpoints (called by Flowable)
    ["/api/tasks/check-eligibility" :post [(body-params/body-params) process-eligibility-task]]
    ["/api/tasks/verify-debtor" :post [(body-params/body-params) process-debtor-verification-task]]
    ["/api/tasks/calculate-discount" :post [(body-params/body-params) process-discount-calculation-task]]})

;; Service configuration
(def service-map
  {::http/routes routes
   ::http/type :jetty
   ::http/port 8090
   ::http/host "localhost"
   ::http/join? false})

(defonce server (atom nil))

(defn start-server []
  (init-db!)
  (reset! server (http/start (http/create-server service-map)))
  (log/info "Server started on http://localhost:8090"))

(defn stop-server []
  (when @server
    (http/stop @server)
    (reset! server nil)
    (log/info "Server stopped")))

(defn -main [& args]
  (start-server))
