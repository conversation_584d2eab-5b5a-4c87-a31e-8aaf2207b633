{:paths ["src" "resources"]
 :deps {org.clojure/clojure {:mvn/version "1.11.1"}
        
        ;; Web framework
        io.pedestal/pedestal.service {:mvn/version "0.6.3"}
        io.pedestal/pedestal.jetty {:mvn/version "0.6.3"}
        
        ;; Database
        org.xerial/sqlite-jdbc {:mvn/version "3.44.1.0"}
        com.github.seancorfield/next.jdbc {:mvn/version "1.3.909"}
        
        ;; HTTP client for Flowable
        clj-http/clj-http {:mvn/version "3.12.3"}
        
        ;; JSON handling
        org.clojure/data.json {:mvn/version "2.4.0"}
        
        ;; Utilities
        prismatic/schema {:mvn/version "1.4.1"}
        org.clojure/tools.logging {:mvn/version "1.2.4"}
        
        ;; Time handling
        clojure.java-time/clojure.java-time {:mvn/version "1.4.2"}}
 
 :aliases {:dev {:extra-deps {org.clojure/tools.namespace {:mvn/version "1.4.4"}}}
           :test {:extra-paths ["test"]
                  :extra-deps {org.clojure/test.check {:mvn/version "1.1.1"}}}
           :flowable {:extra-deps {org.flowable/flowable-engine {:mvn/version "7.0.0"}
                                   org.flowable/flowable-spring-boot-starter {:mvn/version "7.0.0"}}}}}
