(ns trade-finance.test
  (:require [clj-http.client :as client]
            [clojure.data.json :as json]
            [clojure.pprint :as pprint]))

;; Test data
(def sample-invoice
  {:client-id "CLIENT-001"
   :invoice-number "INV-2024-001"
   :amount 50000.00
   :debtor-name "ABC Corporation"})

;; API base URL
(def api-base "http://localhost:8090/api")

(defn submit-test-invoice []
  (println "\n=== Submitting Test Invoice ===")
  (pprint/pprint sample-invoice)
  
  (let [response (client/post (str api-base "/invoice/submit")
                   {:headers {"Content-Type" "application/json"}
                    :body (json/write-str sample-invoice)
                    :as :json})]
    
    (println "\nResponse:")
    (pprint/pprint (:body response))
    
    ;; Return process ID for further testing
    (get-in response [:body :process-id])))

(defn check-invoice-status [process-id]
  (println "\n=== Checking Invoice Status ===")
  (println "Process ID:" process-id)
  
  (let [response (client/get (str api-base "/invoice/status/" process-id)
                   {:as :json})]
    
    (println "\nStatus Response:")
    (pprint/pprint (:body response))))

(defn simulate-external-task [task-type task-data]
  (println "\n=== Simulating External Task:" task-type "===")
  
  (let [endpoint (case task-type
                   :eligibility "/tasks/check-eligibility"
                   :debtor-verification "/tasks/verify-debtor"
                   :discount-calculation "/tasks/calculate-discount")
        
        response (client/post (str api-base endpoint)
                   {:headers {"Content-Type" "application/json"}
                    :body (json/write-str task-data)
                    :as :json})]
    
    (println "Task Response:")
    (pprint/pprint (:body response))))

(defn run-full-test []
  (println "🚀 Starting Trade Finance BPMN Test Flow")
  
  ;; 1. Submit invoice
  (let [process-id (submit-test-invoice)]
    
    ;; 2. Check initial status
    (Thread/sleep 1000)
    (check-invoice-status process-id)
    
    ;; 3. Simulate external tasks (normally called by Flowable)
    (simulate-external-task :eligibility
      {:variables {:client-id {:value "CLIENT-001"}
                   :amount {:value 50000.00}}})
    
    (simulate-external-task :debtor-verification
      {:variables {:debtor-name {:value "ABC Corporation"}}})
    
    (simulate-external-task :discount-calculation
      {:variables {:amount {:value 50000.00}
                   :credit-score {:value 750}}})
    
    ;; 4. Final status check
    (Thread/sleep 1000)
    (check-invoice-status process-id)
    
    (println "\n✅ Test completed! Check your database for stored records.")))

;; Run the test
(comment
  (run-full-test))
