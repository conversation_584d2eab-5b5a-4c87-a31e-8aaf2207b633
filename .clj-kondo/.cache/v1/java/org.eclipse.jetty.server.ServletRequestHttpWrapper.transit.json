["^ ", "~:members", ["^ ", "getDateHeader", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "isRequestedSessionIdValid", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getCookies", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "authenticate", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getPathInfo", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "get<PERSON>art", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getAuthType", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "upgrade", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isRequestedSessionIdFromCookie", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getParts", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "<PERSON><PERSON><PERSON><PERSON>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "<init>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getSession", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "changeSessionId", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getPathTranslated", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getContextPath", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getServletPath", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isUserInRole", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getRequestURL", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getQueryString", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getRequestURI", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getRemoteUser", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getUserPrincipal", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isRequestedSessionIdFromURL", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getMethod", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "login", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "logout", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isRequestedSessionIdFromUrl", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getHeaderNames", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getRequestedSessionId", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getHeaders", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getIntHeader", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]