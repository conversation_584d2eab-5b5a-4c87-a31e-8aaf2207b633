["^ ", "~:members", ["^ ", "setDebug", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "<PERSON><PERSON><PERSON>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "toString", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isRegistered", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "~:static"]]]]], "getInstance", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^9"]]]]], "setPort", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setExitVm", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "register", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^9"]]]]], "<PERSON><PERSON><PERSON>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getPort", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "deregister", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^9"]]]]], "isExitVm", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]