["^ ", "~:members", ["^ ", "getBufferPool", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "opened", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "canWriteWebSocketFrames", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "remoteClose", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "disconnect", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getExecutor", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "toStateString", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getRemoteAddress", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "suspend", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setMaxIdleTimeout", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isReading", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "opening", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getIdleTimeout", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getId", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getLocalAddress", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getPolicy", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "close", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getMaxIdleTimeout", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "canReadWebSocketFrames", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isOpen", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setSession", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setNextIncomingFrames", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]