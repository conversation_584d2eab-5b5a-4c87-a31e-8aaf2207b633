["^ ", "~:members", ["^ ", "<init>", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "getValue", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setByteValue", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setValue", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "lengthInBytes", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "clone", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "toBytes", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "hashCode", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "equals", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "point", ["^2", [["^ ", "^3", ["^2", ["^5", "~:field"]]]]]]]