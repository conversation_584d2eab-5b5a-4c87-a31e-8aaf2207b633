["^ ", "~:members", ["^ ", "MAX_CONTROL_PAYLOAD", ["~#set", [["^ ", "~:flags", ["^2", ["~:public", "~:static", "~:field", "~:final"]]]]], "<init>", ["^2", [["^ ", "^3", ["^2", ["~:method", "^4"]]]]], "<PERSON><PERSON><PERSON><PERSON>", ["^2", [["^ ", "^3", ["^2", ["^9", "^4"]]]]], "equals", ["^2", [["^ ", "^3", ["^2", ["^9", "^4"]]]]], "isControlFrame", ["^2", [["^ ", "^3", ["^2", ["^9", "^4"]]]]], "isDataFrame", ["^2", [["^ ", "^3", ["^2", ["^9", "^4"]]]]], "setPayload", ["^2", [["^ ", "^3", ["^2", ["^9", "^4"]]]]], "getPayload", ["^2", [["^ ", "^3", ["^2", ["^9", "^4"]]]]]]]