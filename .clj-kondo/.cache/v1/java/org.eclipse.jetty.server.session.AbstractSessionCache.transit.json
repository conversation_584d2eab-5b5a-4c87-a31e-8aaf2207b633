["^ ", "~:members", ["^ ", "release", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "renewSessionId", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setRemoveUnloadableSessions", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isSaveOnInactiveEviction", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setEvictionPolicy", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "delete", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "put", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "newSession", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setSaveOnInactiveEviction", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getEvictionPolicy", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "contains", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "<init>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setSaveOnCreate", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "toString", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setFlushOnResponseCommit", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getSessionHandler", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "doDelete", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "commit", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "checkExpiration", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isInvalidateOnShutdown", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isFlushOnResponseCommit", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "initialize", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "checkInactiveSession", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setInvalidateOnShutdown", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "add", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "exists", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isSaveOnCreate", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "get", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isRemoveUnloadableSessions", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setSessionDataStore", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getSessionDataStore", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]