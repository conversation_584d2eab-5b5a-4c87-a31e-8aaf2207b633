["^ ", "~:members", ["^ ", "setFlushedLSN", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "readPending", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setAppliedLSN", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getLastAppliedLSN", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "<init>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "read", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "forceUpdateStatus", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isClosed", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getLastReceiveLSN", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "POSTGRES_EPOCH_2000_01_01", ["^2", [["^ ", "^3", ["^2", ["^5", "~:static", "~:field", "~:final"]]]]], "close", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getLastFlushedLSN", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]