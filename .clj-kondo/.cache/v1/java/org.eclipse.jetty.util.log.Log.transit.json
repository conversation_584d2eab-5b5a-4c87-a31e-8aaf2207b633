["^ ", "~:members", ["^ ", "IGNORED", ["~#set", [["^ ", "~:flags", ["^2", ["~:public", "~:static", "~:field", "~:final"]]]]], "EXCEPTION", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6", "^7"]]]]], "getLog", ["^2", [["^ ", "^3", ["^2", ["~:method", "^4", "^5"]]]]], "setLog", ["^2", [["^ ", "^3", ["^2", ["^:", "^4", "^5"]]]]], "<init>", ["^2", [["^ ", "^3", ["^2", ["^:", "^4"]]]]], "get<PERSON><PERSON><PERSON>og<PERSON>", ["^2", [["^ ", "^3", ["^2", ["^:", "^4", "^5"]]]]], "setLogToParent", ["^2", [["^ ", "^3", ["^2", ["^:", "^4", "^5"]]]]], "initialized", ["^2", [["^ ", "^3", ["^2", ["^:", "^4", "^5"]]]]], "getProperties", ["^2", [["^ ", "^3", ["^2", ["^:", "^4", "^5"]]]]], "getLoggers", ["^2", [["^ ", "^3", ["^2", ["^:", "^4", "^5"]]]]], "__ignored", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "<PERSON><PERSON><PERSON><PERSON>", ["^2", [["^ ", "^3", ["^2", ["^:", "^4", "^5"]]]]], "__logClass", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]]]]