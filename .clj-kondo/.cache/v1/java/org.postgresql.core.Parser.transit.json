["^ ", "~:members", ["^ ", "isDigitAt", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public", "~:static"]]]]], "isOperatorChar", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "modifyJdbcCall", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "isSpace", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "isDollarQuoteStartChar", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "parseLineComment", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "parseAsKeyword", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "parseWithKeyword", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "parseSelectKeyword", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "<init>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "parseDeleteKeyword", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "parseBeginKeyword", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "parseCreateKeyword", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "parseAtomicKeyword", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "digitAt", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "isDollarQuoteContChar", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "parseSingleQuotes", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "isIdentifierStartChar", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "isArrayWhiteSpace", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "parseLong", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "parseAlterKeyword", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "parseDollar<PERSON><PERSON><PERSON>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "parseMoveKeyword", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "parseInsertKeyword", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "parseValuesKeyword", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "parseReturningKeyword", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "isIdentifierContChar", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "charTerminatesIdentifier", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "replaceProcessing", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "parseUpdateKeyword", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "parseBlockComment", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "parseJdbcSql", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "parseDoubleQuotes", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]]]]