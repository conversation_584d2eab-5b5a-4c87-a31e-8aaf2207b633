["^ ", "~:members", ["^ ", "contains", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "<init>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "toString", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "iterator", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "remove", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "hashCode", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "add", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isEmpty", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "size", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "retainAll", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "clear", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "toArray", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "removeAll", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "equals", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "containsAll", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]