["^ ", "~:members", ["^ ", "getParameterStatus", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "getAutosave", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getBackendPID", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getPreferQueryMode", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getPrepareThreshold", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getParameterStatuses", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setPrepareThreshold", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getCopyAPI", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getDefaultFetchSize", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "addDataType", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "escapeIdentifier", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getNotifications", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getLargeObjectAPI", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getFastpathAPI", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setAutosave", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setAdaptiveFetch", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "cancelQuery", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setDefaultFetchSize", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getReplicationAPI", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "escapeLiteral", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getAdaptiveFetch", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "createArrayOf", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]