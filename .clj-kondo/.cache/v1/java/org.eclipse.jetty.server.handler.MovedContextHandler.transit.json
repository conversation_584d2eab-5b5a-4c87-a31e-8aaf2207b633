["^ ", "~:members", ["^ ", "setExpires", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isDiscardPathInfo", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setPermanent", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setDiscardPathInfo", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "<init>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isPermanent", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getExpires", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setNewContextURL", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "is<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getNewContextURL", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]