["^ ", "~:members", ["^ ", "onTextFrame", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "onClose", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "onTextMessage", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "onBinaryFrame", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "<init>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "onConnect", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "onError", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "toString", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "onReader", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "onBinaryMessage", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "onFrame", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "onContinuationFrame", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "onInputStream", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]