["^ ", "~:members", ["^ ", "setConfig", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "getBufferPool", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isRsv3User", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setPolicy", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getNextOutgoing", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "<init>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "toString", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setNextOutgoingFrames", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getName", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isRsv2User", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setConnection", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getNextIncoming", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getPolicy", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setBufferPool", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "init", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getConnection", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isRsv1User", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getConfig", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setNextIncomingFrames", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]