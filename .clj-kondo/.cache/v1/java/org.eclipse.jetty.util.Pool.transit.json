["^ ", "~:members", ["^ ", "release", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "values", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getIdleCount", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "reserve", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getReservedCount", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setMaxMultiplex", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "~:final"]]]]], "<init>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getMaxUsageCount", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "toString", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isClosed", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "remove", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "dump", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "close", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "acquireAt", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getClosedCount", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setMaxUsageCount", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^;"]]]]], "size", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getMaxEntries", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getMaxMultiplex", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "acquire", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getInUseCount", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]