["^ ", "~:members", ["^ ", "getPrepareThreshold", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "isUseServerPrepare", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setPrepareThreshold", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "DATE_POSITIVE_INFINITY", ["^2", [["^ ", "^3", ["^2", ["^5", "~:static", "~:field", "~:final"]]]]], "getLastOID", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "DATE_NEGATIVE_SMALLER_INFINITY", ["^2", [["^ ", "^3", ["^2", ["^5", "^9", "^:", "^;"]]]]], "setAdaptiveFetch", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "DATE_NEGATIVE_INFINITY", ["^2", [["^ ", "^3", ["^2", ["^5", "^9", "^:", "^;"]]]]], "setUseServerPrepare", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getAdaptiveFetch", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "DATE_POSITIVE_SMALLER_INFINITY", ["^2", [["^ ", "^3", ["^2", ["^5", "^9", "^:", "^;"]]]]]]]