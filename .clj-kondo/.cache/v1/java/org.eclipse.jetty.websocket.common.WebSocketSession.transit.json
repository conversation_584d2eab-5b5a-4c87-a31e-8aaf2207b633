["^ ", "~:members", ["^ ", "getUpgradeRequest", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "getExtensionFactory", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "callApplicationOnError", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setFuture", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getWebSocketSession", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "dispatch", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getBufferPool", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getRemote", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setPolicy", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "newRemoteEndpoint", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "<PERSON><PERSON><PERSON><PERSON>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setOutgoingHandler", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getUpgradeResponse", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "disconnect", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getRemoteAddress", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "suspend", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "<init>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "toString", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getContainerScope", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isSecure", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setUpgradeResponse", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getIdleTimeout", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "dumpSelf", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getClassLoader", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getLocalAddress", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getPolicy", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getOutgoingHandler", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getRequestURI", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "close", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "onOpened", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "onClosed", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "incoming<PERSON><PERSON><PERSON>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getConnection", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "callApplicationOnClose", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getBatchMode", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setUpgradeRequest", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isOpen", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setIdleTimeout", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getIncomingHandler", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "open", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setExtensionFactory", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getProtocolVersion", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]