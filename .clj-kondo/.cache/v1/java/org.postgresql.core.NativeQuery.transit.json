["^ ", "~:members", ["^ ", "multiStatement", ["~#set", [["^ ", "~:flags", ["^2", ["~:public", "~:field", "~:final"]]]]], "command", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "nativeSql", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "getCommand", ["^2", [["^ ", "^3", ["^2", ["~:method", "^4"]]]]], "<init>", ["^2", [["^ ", "^3", ["^2", ["^:", "^4"]]]]], "toString", ["^2", [["^ ", "^3", ["^2", ["^:", "^4"]]]]], "bindName", ["^2", [["^ ", "^3", ["^2", ["^:", "^4", "~:static"]]]]], "calculateBindLength", ["^2", [["^ ", "^3", ["^2", ["^:", "^4", "^>"]]]]], "bindPositions", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "appendBindName", ["^2", [["^ ", "^3", ["^2", ["^:", "^4", "^>"]]]]]]]