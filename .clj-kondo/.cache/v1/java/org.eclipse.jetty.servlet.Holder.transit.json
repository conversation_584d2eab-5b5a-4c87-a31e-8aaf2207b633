["^ ", "~:members", ["^ ", "setAsyncSupported", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "setDisplayName", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setName", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "toString", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "destroyInstance", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getDisplayName", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getName", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setClassName", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getInitParameter", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "dump", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setInitParameter", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getInitParameters", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setInitParameters", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isAsyncSupported", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setHeldClass", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getInitParameterNames", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]