["^ ", "~:members", ["^ ", "asIntegerValue", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "getByte", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getType", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isIntegerValue", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getShort", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "<init>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getLong", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getInt", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getBigInteger", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]