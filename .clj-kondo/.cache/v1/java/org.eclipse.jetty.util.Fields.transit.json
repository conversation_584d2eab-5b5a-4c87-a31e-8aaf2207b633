["^ ", "~:members", ["^ ", "put", ["~#set", [["^ ", "~:flags", ["^1", ["~:method", "~:public"]]]]], "<init>", ["^1", [["^ ", "^2", ["^1", ["^3", "^4"]]]]], "toString", ["^1", [["^ ", "^2", ["^1", ["^3", "^4"]]]]], "iterator", ["^1", [["^ ", "^2", ["^1", ["^3", "^4"]]]]], "remove", ["^1", [["^ ", "^2", ["^1", ["^3", "^4"]]]]], "hashCode", ["^1", [["^ ", "^2", ["^1", ["^3", "^4"]]]]], "add", ["^1", [["^ ", "^2", ["^1", ["^3", "^4"]]]]], "isEmpty", ["^1", [["^ ", "^2", ["^1", ["^3", "^4"]]]]], "clear", ["^1", [["^ ", "^2", ["^1", ["^3", "^4"]]]]], "getSize", ["^1", [["^ ", "^2", ["^1", ["^3", "^4"]]]]], "get", ["^1", [["^ ", "^2", ["^1", ["^3", "^4"]]]]], "equals", ["^1", [["^ ", "^2", ["^1", ["^3", "^4"]]]]], "getNames", ["^1", [["^ ", "^2", ["^1", ["^3", "^4"]]]]]]]