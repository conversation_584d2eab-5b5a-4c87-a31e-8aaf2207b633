["^ ", "~:members", ["^ ", "toStarString", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "<init>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "toString", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "obfuscate", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "~:static"]]]]], "main", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^9"]]]]], "check", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "deobfuscate", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^9"]]]]], "hashCode", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "equals", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getPassword", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^9"]]]]], "__OBFUSCATE", ["^2", [["^ ", "^3", ["^2", ["^5", "^9", "~:field", "~:final"]]]]]]]