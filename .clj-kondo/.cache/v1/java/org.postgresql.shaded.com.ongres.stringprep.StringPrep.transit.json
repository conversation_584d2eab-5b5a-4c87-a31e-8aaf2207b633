["^ ", "~:members", ["^ ", "prohibitionNonAsciiControl", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public", "~:static"]]]]], "bidirectionalPropertyL", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "mapUsedWithNoNormalization", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "unassignedCodePoints", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "prohibitionNonCharacterCodePoints", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "bidirectionalPropertyRorAL", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "<init>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "prohibitionSurrogateCodes", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "prohibitionChangeDisplayProperties", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "prohibitionPrivateUse", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "prohibitionInappropriateCanonicalRepresentation", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "mapUsedWithNfkc", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "prohibitionAsciiSpace", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "prohibitionAsciiControl", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "prohibitionNonAsciiSpace", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "prohibitionInappropriatePlainText", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "bidirectional", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "mapToNothing", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "prohibitionTaggingCharacters", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]]]]