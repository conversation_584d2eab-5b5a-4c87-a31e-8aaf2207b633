["^ ", "~:members", ["^ ", "values", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public", "~:static"]]]]], "DISABLE", ["^2", [["^ ", "^3", ["^2", ["^5", "^6", "~:field", "~:final"]]]]], "valueOf", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "of", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "requireEncryption", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "value", ["^2", [["^ ", "^3", ["^2", ["^5", "^8", "^9"]]]]], "PREFER", ["^2", [["^ ", "^3", ["^2", ["^5", "^6", "^8", "^9"]]]]], "REQUIRE", ["^2", [["^ ", "^3", ["^2", ["^5", "^6", "^8", "^9"]]]]], "ALLOW", ["^2", [["^ ", "^3", ["^2", ["^5", "^6", "^8", "^9"]]]]]]]