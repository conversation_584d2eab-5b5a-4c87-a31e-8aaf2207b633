["^ ", "~:members", ["^ ", "getReportExistingFilesOnStartup", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "addDirectory", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setReportDirs", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "DEFAULT_SCAN_DEPTH", ["^2", [["^ ", "^3", ["^2", ["^5", "~:static", "~:field", "~:final"]]]]], "setReportExistingFilesOnStartup", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "addScanDir", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setScanDirs", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "scanFiles", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "<init>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setScanInterval", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "reset", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getScanDirs", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "doStop", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getReportDirs", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "schedule", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "scan", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getRecursive", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "removeListener", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "addListener", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setScanDepth", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "addFile", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "newTimerTask", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getScan<PERSON>epth", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getScannables", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "exists", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setFilenameFilter", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setRecursive", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "newTimer", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "doStart", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getScanInterval", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "MAX_SCAN_DEPTH", ["^2", [["^ ", "^3", ["^2", ["^5", "^9", "^:", "^;"]]]]], "getFilenameFilter", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]