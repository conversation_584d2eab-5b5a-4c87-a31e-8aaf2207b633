["^ ", "~:members", ["^ ", "<init>", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "getCharacterStream", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setString", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setAsciiStream", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setCharacterStream", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getAsciiStream", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getSubString", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "position", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]