["^ ", "~:members", ["^ ", "<init>", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "add<PERSON>ew<PERSON><PERSON><PERSON>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "updateQueryFetchSize", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getFetchSizeForQuery", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "<PERSON><PERSON><PERSON><PERSON>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getAdaptiveFetch", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setAdaptiveFetch", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]