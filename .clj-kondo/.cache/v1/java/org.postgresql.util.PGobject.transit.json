["^ ", "~:members", ["^ ", "getType", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public", "~:final"]]]]], "isNull", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setType", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "<init>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "toString", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getValue", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setValue", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "clone", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "hashCode", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "equals", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]