["^ ", "~:members", ["^ ", "nullsAreSortedHigh", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "autoCommitFailureClosesAllResultSets", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getDatabaseProductVersion", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsStoredProcedures", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getMaxColumnsInTable", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getFunctions", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getMaxStatementLength", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsStatementPooling", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "othersDeletesAreVisible", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getExportedKeys", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "storesLowerCaseQuotedIdentifiers", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsANSI92EntryLevelSQL", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "storesUpperCaseQuotedIdentifiers", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "othersUpdatesAreVisible", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getMaxColumnsInGroupBy", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "updatesAreDetected", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "ownUpdatesAreVisible", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "usesLocalFiles", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsBatchUpdates", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getExtraNameCharacters", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getDatabaseMinorVersion", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getMaxIndexLength", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getTypeInfo", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsLimitedOuterJoins", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsConvert", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsColumnAliasing", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsSchemasInTableDefinitions", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getMaxConnections", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "storesMixedCaseIdentifiers", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getProcedureTerm", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsPositionedDelete", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getPrimaryKeys", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "storesMixedCaseQuotedIdentifiers", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsSelectForUpdate", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getMaxLogicalLobSize", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getIdentifierQuoteString", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsSchemasInIndexDefinitions", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getMaxProcedureNameLength", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "parseACL", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getMaxColumnsInIndex", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getSchemas", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsSubqueriesInComparisons", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "dataDefinitionCausesTransactionCommit", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsSavepoints", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsIntegrityEnhancementFacility", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getColumns", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsSchemasInProcedureCalls", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getJDBCMinorVersion", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getTableTypes", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getTables", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getRowIdLifetime", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "<init>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsDataDefinitionAndDataManipulationTransactions", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsANSI92IntermediateSQL", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getVersionColumns", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsRefCursors", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsSchemasInDataManipulation", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getUserName", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsLikeEscapeClause", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsCatalogsInPrivilegeDefinitions", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getDriverMinorVersion", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getTimeDateFunctions", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getColumnPrivileges", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getImportedKeys", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getSearchStringEscape", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "unwrap", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsCoreSQLGrammar", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getDriverName", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsSubqueriesInQuantifieds", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsGetGeneratedKeys", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsPositionedUpdate", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsExpressionsInOrderBy", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getMaxBinaryLiteralLength", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getResultSetHoldability", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getClientInfoProperties", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getSchemaTerm", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsSubqueriesInIns", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getBestRowIdentifier", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getCatalogs", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsTransactions", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsSubqueriesInExists", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsCatalogsInProcedureCalls", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "nullPlusNonNullIsNull", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getMaxColumnNameLength", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsDifferentTableCorrelationNames", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getMaxTablesInSelect", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "generatedKeyAlwaysReturned", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getMaxColumnsInSelect", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getSQLKeywords", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "ownInsertsAreVisible", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "othersInsertsAreVisible", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getSystemFunctions", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getSuperTables", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsTableCorrelationNames", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "allProceduresAreCallable", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getProcedures", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsTransactionIsolationLevel", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getMaxUserNameLength", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsNamedParameters", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getMaxCharLiteralLength", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsOpenCursorsAcrossRollback", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getIndexInfo", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "insertsAreDetected", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "allTablesAreSelectable", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsResultSetHoldability", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getMaxRowSize", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getStringFunctions", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getUDTs", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getDatabaseProductName", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsUnionAll", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsFullOuterJoins", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getNumericFunctions", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsMultipleTransactions", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsCatalogsInTableDefinitions", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsOrderByUnrelated", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsMinimumSQLGrammar", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getSQLStateType", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "locatorsUpdateCopy", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getMaxSchemaNameLength", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsUnion", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "doesMaxRowSizeIncludeBlobs", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getDefaultTransactionIsolation", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsGroupByUnrelated", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsDataManipulationTransactionsOnly", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getCatalogSeparator", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsGroupBy", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getMaxCursorNameLength", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getConnection", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsCatalogsInDataManipulation", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getPseudoColumns", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsCorrelatedSubqueries", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsStoredFunctionsUsingCallSyntax", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "nullsAreSortedAtEnd", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getCatalogTerm", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsAlterTableWithDropColumn", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getProcedureColumns", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsMultipleResultSets", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "storesUpperCaseIdentifiers", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsAlterTableWithAddColumn", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getURL", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "ownDeletesAreVisible", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getDriverMajorVersion", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "dataDefinitionIgnoredInTransactions", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getCrossReference", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsOuterJoins", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getDriverVersion", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsGroupByBeyondSelect", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isReadOnly", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsOpenStatementsAcrossCommit", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsExtendedSQLGrammar", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsMixedCaseQuotedIdentifiers", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsMixedCaseIdentifiers", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getMaxCatalogNameLength", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "storesLowerCaseIdentifiers", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsResultSetConcurrency", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "nullsAreSortedLow", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsCatalogsInIndexDefinitions", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsNonNullableColumns", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getJDBCMajorVersion", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getMaxColumnsInOrderBy", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "usesLocalFilePerTable", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsOpenCursorsAcrossCommit", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "nullsAreSortedAtStart", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getAttributes", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getTablePrivileges", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isWrapperFor", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isCatalogAtStart", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsOpenStatementsAcrossRollback", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsANSI92FullSQL", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsMultipleOpenResults", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getMaxStatements", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getDatabaseMajorVersion", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsResultSetType", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "deletesAreDetected", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getMaxTableNameLength", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getFunctionColumns", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "supportsSchemasInPrivilegeDefinitions", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getSuperTypes", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]