["^ ", "~:members", ["^ ", "peek", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "DEFAULT_CAPACITY", ["^2", [["^ ", "^3", ["^2", ["^5", "~:static", "~:field", "~:final"]]]]], "offer", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "put", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getMaxCapacity", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "<init>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "take", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getCapacity", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "listIterator", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "iterator", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "element", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "remove", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "drainTo", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "poll", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "DEFAULT_GROWTH", ["^2", [["^ ", "^3", ["^2", ["^5", "^7", "^8", "^9"]]]]], "remainingCapacity", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "add", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "set", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "size", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "clear", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "get", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]