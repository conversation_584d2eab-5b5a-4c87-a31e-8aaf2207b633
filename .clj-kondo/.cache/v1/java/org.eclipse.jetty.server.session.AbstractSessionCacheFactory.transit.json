["^ ", "~:members", ["^ ", "getSessionCache", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "setRemoveUnloadableSessions", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setEvictionPolicy", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getEvictionPolicy", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "<init>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setSaveOnInactiveEvict", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setSaveOnCreate", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setFlushOnResponseCommit", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isInvalidateOnShutdown", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isFlushOnResponseCommit", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isSaveOnInactiveEvict", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setInvalidateOnShutdown", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isSaveOnCreate", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "newSessionCache", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isRemoveUnloadableSessions", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]