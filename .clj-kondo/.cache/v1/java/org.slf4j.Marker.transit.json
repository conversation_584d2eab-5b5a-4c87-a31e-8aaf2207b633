["^ ", "~:members", ["^ ", "contains", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "iterator", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getName", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "remove", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "ANY_MARKER", ["^2", [["^ ", "^3", ["^2", ["^5", "~:static", "~:field", "~:final"]]]]], "ANY_NON_NULL_MARKER", ["^2", [["^ ", "^3", ["^2", ["^5", "^:", "^;", "^<"]]]]], "hashCode", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "add", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "hasReferences", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "<PERSON><PERSON><PERSON><PERSON><PERSON>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "equals", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]