["^ ", "~:members", ["^ ", "parseURL", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public", "~:static"]]]]], "getMinorVersion", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "<init>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "get<PERSON><PERSON>nt<PERSON>og<PERSON>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getPropertyInfo", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isRegistered", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "connect", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getSharedTimer", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "getVersion", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "acceptsURL", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "register", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "notImplemented", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "jdbcCompliant", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "deregister", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "getMajorVersion", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]