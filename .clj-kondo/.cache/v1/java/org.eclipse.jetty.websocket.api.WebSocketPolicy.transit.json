["^ ", "~:members", ["^ ", "delegateAs", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "getMaxBinaryMessageBufferSize", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setInputBufferSize", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "<init>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getAsyncWriteTimeout", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "newClientPolicy", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "~:static"]]]]], "toString", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getMaxTextMessageBufferSize", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setMaxBinaryMessageBufferSize", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "newServerPolicy", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^;"]]]]], "get<PERSON><PERSON><PERSON><PERSON>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getIdleTimeout", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getInputBufferSize", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setMaxBinaryMessageSize", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "assertValidTextMessageSize", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setIdleTimeout", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setMaxTextMessageSize", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "assertValidBinaryMessageSize", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getMaxBinaryMessageSize", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setAsyncWriteTimeout", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setMaxTextMessageBufferSize", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "clonePolicy", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getMaxTextMessageSize", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]