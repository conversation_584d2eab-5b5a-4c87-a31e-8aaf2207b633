["^ ", "~:members", ["^ ", "getExtensionNames", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "newInstance", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "unregister", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "<init>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getExtension", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "iterator", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getAvailableExtensions", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "register", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isAvailable", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]