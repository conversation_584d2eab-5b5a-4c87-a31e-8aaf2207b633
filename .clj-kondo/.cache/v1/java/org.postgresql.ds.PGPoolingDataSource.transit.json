["^ ", "~:members", ["^ ", "setDatabaseName", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "setMaxConnections", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getMaxConnections", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setPassword", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setServerName", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "<init>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getReference", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "unwrap", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setPortNumber", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getInitialConnections", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getDescription", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setInitialConnections", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "close", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "initialize", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setUser", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getConnection", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getDataSourceName", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setDataSourceName", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isWrapperFor", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getDataSource", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "~:static"]]]]]]]