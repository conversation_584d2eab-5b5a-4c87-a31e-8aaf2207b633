["^ ", "~:members", ["^ ", "setSslSessionAttribute", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "customize", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setSniHostCheck", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setStsMaxAge", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "<init>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "toString", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setSniRequired", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "JAVAX_SERVLET_REQUEST_SSL_SESSION_ID", ["^2", [["^ ", "^3", ["^2", ["^5", "~:static", "~:field", "~:final"]]]]], "isSniHostCheck", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isSniRequired", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "JAVAX_SERVLET_REQUEST_CIPHER_SUITE", ["^2", [["^ ", "^3", ["^2", ["^5", "^=", "^>", "^?"]]]]], "setStsIncludeSubDomains", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getStsMaxAge", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "JAVAX_SERVLET_REQUEST_KEY_SIZE", ["^2", [["^ ", "^3", ["^2", ["^5", "^=", "^>", "^?"]]]]], "JAVAX_SERVLET_REQUEST_X_509_CERTIFICATE", ["^2", [["^ ", "^3", ["^2", ["^5", "^=", "^>", "^?"]]]]], "isStsIncludeSubDomains", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getSslSessionAttribute", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]