["^ ", "~:members", ["^ ", "get<PERSON><PERSON><PERSON>pal", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "is<PERSON><PERSON>in", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setRequestURI", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getCookies", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "<PERSON><PERSON><PERSON><PERSON>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setSubProtocols", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getRequestPath", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getHeaderInt", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getLocalPort", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "set<PERSON>ethod", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getHttpServletRequest", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setServletAttribute", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setExtensions", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getRemoteHostName", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "<PERSON><PERSON><PERSON><PERSON>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getParameterMap", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getRemoteAddress", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "<init>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "complete", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getRemotePort", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getLocale", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getSubProtocols", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getExtensions", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getServletAttributes", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getLocales", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getSession", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getHttpVersion", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setHeaders", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getRemoteSocketAddress", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isSecure", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "<PERSON><PERSON><PERSON><PERSON>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "addExtensions", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isUserInRole", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setCookies", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getLocalSocketAddress", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getQueryString", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getLocalAddress", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getRequestURI", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "hasSubProtocol", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "clearHeaders", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getCertificates", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getUserPrincipal", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setHttpVersion", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getMethod", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getHost", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getServletAttribute", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getServletParameters", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getHeaders", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setSession", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getProtocolVersion", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getLocalHostName", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]