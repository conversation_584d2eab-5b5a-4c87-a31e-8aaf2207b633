["^ ", "~:members", ["^ ", "stop", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public", "~:static"]]], ["^ ", "^3", ["^2", ["^4", "^5"]]]]], "addLifeCycleListener", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isStopped", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isStopping", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isStarted", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "start", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]], ["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isStarting", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isRunning", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "removeLifeCycleListener", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isFailed", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]