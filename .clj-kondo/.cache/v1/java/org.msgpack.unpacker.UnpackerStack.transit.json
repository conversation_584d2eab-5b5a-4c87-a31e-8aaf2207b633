["^ ", "~:members", ["^ ", "topIsArray", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "pushMap", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "pop", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "<init>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "topIsMap", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "pushArray", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getTopCount", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "checkCount", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "reduceCount", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "MAX_STACK_SIZE", ["^2", [["^ ", "^3", ["^2", ["^5", "~:static", "~:field", "~:final"]]]]], "clear", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "<PERSON><PERSON><PERSON>h", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]