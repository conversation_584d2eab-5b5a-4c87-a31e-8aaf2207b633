["^ ", "~:members", ["^ ", "cacheSQLTypes", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "getJavaArrayType", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getPrecision", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getJavaClass", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getPGArrayElement", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getPGArrayType", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "<init>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "addDataType", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "intOidToLong", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getMaximumPrecision", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getPGType", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getDisplaySize", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getTypeForAlias", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getArrayDelimiter", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getScale", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getPGTypeOidsWithSQLTypes", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "requiresQuotingSqlType", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getSQLType", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "requiresQuoting", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isSigned", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "longOidToInt", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getPGTypeNamesWithSQLTypes", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "addCoreType", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getPGobject", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isCaseSensitive", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]