["^ ", "~:members", ["^ ", "newConstantDynamic", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "newPackage", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "COMPUTE_FRAMES", ["^2", [["^ ", "^3", ["^2", ["^5", "~:static", "~:field", "~:final"]]]]], "newModule", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "visitNestMember", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^:"]]]]], "visitRecordComponent", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^:"]]]]], "visit", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^:"]]]]], "visitEnd", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^:"]]]]], "visitAnnotation", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^:"]]]]], "COMPUTE_MAXS", ["^2", [["^ ", "^3", ["^2", ["^5", "^8", "^9", "^:"]]]]], "<init>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "newMethod", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "visitMethod", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^:"]]]]], "visitPermittedSubclass", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^:"]]]]], "visitTypeAnnotation", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^:"]]]]], "newClass", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "visitInnerClass", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^:"]]]]], "visitModule", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^:"]]]]], "visitSource", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^:"]]]]], "newMethodType", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "visitNestHost", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^:"]]]]], "visitAttribute", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^:"]]]]], "newUTF8", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "newInvokeDynamic", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "newNameType", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "visitOuterClass", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^:"]]]]], "newHandle", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "newConst", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "visitField", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^:"]]]]], "toByteArray", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "newField", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]