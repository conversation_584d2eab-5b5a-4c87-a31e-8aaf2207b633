["^ ", "~:members", ["^ ", "createClob", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "getAutoCommit", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getParameterStatus", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "~:final"]]]]], "getAutosave", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "createQueryObject", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getReplicationProtocol", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setTypeMap", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setReadOnly", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getBackendPID", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getPreferQueryMode", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "purgeTimerTasks", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getPrepareThreshold", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getEncoding", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getParameterStatuses", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^8"]]]]], "getTypeInfo", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "execSQLUpdate", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "haveMinimumServerVersion", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getTransactionIsolation", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setPrepareThreshold", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getServerMinorVersion", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getCopyAPI", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setSavepoint", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "addTimerTask", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "<init>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getDefaultFetchSize", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "addDataType", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "escapeIdentifier", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getNotifications", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getUserName", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "nativeSQL", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "clearWarnings", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "prepareStatement", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getWarnings", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getLargeObjectAPI", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getObject", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setClientInfo", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getFieldMetadataCache", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getClientInfo", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "unwrap", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "createNClob", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "releaseSavepoint", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getFastpathAPI", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "createSQLXML", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setDisableColumnSanitiser", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setAutosave", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "prepareCall", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "encodeString", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setTransactionIsolation", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "escapeString", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "commit", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "createStatement", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getXmlFactoryFactory", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isClosed", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "createBlob", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getCursorName", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setCursorName", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setAdaptiveFetch", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "cancelQuery", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isColumnSanitiserDisabled", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setDefaultFetchSize", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setTypeMapImpl", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "hintReadOnly", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getStringVarcharFlag", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getCatalog", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getLogServerErrorDetail", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setSchema", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getStandardConformingStrings", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getMetaData", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getTransactionState", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setForceBinary", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "close", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "execSQLQuery", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "<PERSON><PERSON><PERSON><PERSON>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "createQuery", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getReplicationAPI", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getSchema", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getDBVersionNumber", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setCatalog", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "binaryTransferSend", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getHideUnprivilegedObjects", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getServerMajorVersion", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setHoldability", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "rollback", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getURL", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getTypeMap", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "escapeLiteral", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "abort", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getForceBinary", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getHoldability", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "createStruct", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getQueryExecutor", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "<PERSON><PERSON><PERSON><PERSON>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isReadOnly", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getAdaptiveFetch", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setFlushCacheOnDeallocate", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setAutoCommit", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "addWarning", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getNetworkTimeout", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isWrapperFor", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getTimestampUtils", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setNetworkTimeout", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "createArrayOf", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getProtocolVersion", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]