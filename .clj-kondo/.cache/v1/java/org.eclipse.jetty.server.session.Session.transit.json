["^ ", "~:members", ["^ ", "isResident", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "isIdChanged", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "willPassivate", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "didActivate", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "<init>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "renewId", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "putValue", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "removeValue", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "toString", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setResident", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getSessionHandler", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getSession", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setIdChanged", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getVHost", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getValue", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getAttribute", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "bindValue", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getContextPath", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "removeAttribute", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isNew", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getRequests", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "updateInactivityTimer", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "unbindValue", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setAttribute", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "invalidate", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getId", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "<PERSON><PERSON><PERSON><PERSON>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getSessionContext", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getCookieSetTime", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "lock", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getValueNames", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setMaxInactiveInterval", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "calculateInactivityTimeout", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getExtendedId", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "SESSION_CREATED_SECURE", ["^2", [["^ ", "^3", ["^2", ["^5", "~:static", "~:field", "~:final"]]]]], "setExtendedId", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getAttributeNames", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isInvalid", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isChanging", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getAttributes", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getLastAccessedTime", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getCreationTime", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getNames", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getServletContext", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getMaxInactiveInterval", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]