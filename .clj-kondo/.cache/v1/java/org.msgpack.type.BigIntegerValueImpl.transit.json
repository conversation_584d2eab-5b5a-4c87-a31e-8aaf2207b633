["^ ", "~:members", ["^ ", "getByte", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "shortValue", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "doubleValue", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "longValue", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "byteValue", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getShort", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "toString", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "bigIntegerValue", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getLong", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "floatValue", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getInt", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "writeTo", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "intValue", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "hashCode", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "equals", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getBigInteger", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]