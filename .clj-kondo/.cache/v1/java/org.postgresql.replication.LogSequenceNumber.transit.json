["^ ", "~:members", ["^ ", "INVALID_LSN", ["~#set", [["^ ", "~:flags", ["^2", ["~:public", "~:static", "~:field", "~:final"]]]]], "valueOf", ["^2", [["^ ", "^3", ["^2", ["~:method", "^4", "^5"]]]]], "asLong", ["^2", [["^ ", "^3", ["^2", ["^9", "^4"]]]]], "asString", ["^2", [["^ ", "^3", ["^2", ["^9", "^4"]]]]], "equals", ["^2", [["^ ", "^3", ["^2", ["^9", "^4"]]]]], "hashCode", ["^2", [["^ ", "^3", ["^2", ["^9", "^4"]]]]], "toString", ["^2", [["^ ", "^3", ["^2", ["^9", "^4"]]]]], "compareTo", ["^2", [["^ ", "^3", ["^2", ["^9", "^4"]]]]]]]