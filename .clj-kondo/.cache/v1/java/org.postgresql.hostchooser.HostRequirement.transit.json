["^ ", "~:members", ["^ ", "allowConnectingTo", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "values", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "~:static"]]]]], "preferPrimary", ["^2", [["^ ", "^3", ["^2", ["^5", "^7", "~:field", "~:final"]]]]], "valueOf", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^7"]]]]], "secondary", ["^2", [["^ ", "^3", ["^2", ["^5", "^7", "^9", "^:"]]]]], "getTargetServerType", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^7"]]]]], "any", ["^2", [["^ ", "^3", ["^2", ["^5", "^7", "^9", "^:"]]]]], "primary", ["^2", [["^ ", "^3", ["^2", ["^5", "^7", "^9", "^:"]]]]], "master", ["^2", [["^ ", "^3", ["^2", ["^5", "^7", "^9", "^:"]]]]], "preferSecondary", ["^2", [["^ ", "^3", ["^2", ["^5", "^7", "^9", "^:"]]]]]]]