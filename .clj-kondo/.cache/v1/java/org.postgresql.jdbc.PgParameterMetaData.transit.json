["^ ", "~:members", ["^ ", "getPrecision", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "getParameterCount", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "<init>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "unwrap", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getScale", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getParameterTypeName", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getParameterClassName", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isSigned", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getParameterType", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isNullable", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getParameterMode", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isWrapperFor", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]