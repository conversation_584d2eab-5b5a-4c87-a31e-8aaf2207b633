["^ ", "~:members", ["^ ", "getUpgradeRequest", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "getRemote", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getUpgradeResponse", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "disconnect", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getRemoteAddress", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "suspend", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isSecure", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getIdleTimeout", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getLocalAddress", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getPolicy", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "close", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isOpen", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setIdleTimeout", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getProtocolVersion", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]