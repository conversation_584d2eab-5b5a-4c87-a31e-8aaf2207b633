["^ ", "~:members", ["^ ", "SEEK_END", ["~#set", [["^ ", "~:flags", ["^2", ["~:public", "~:static", "~:field", "~:final"]]]]], "tell", ["^2", [["^ ", "^3", ["^2", ["~:method", "^4"]]]]], "copy", ["^2", [["^ ", "^3", ["^2", ["^9", "^4"]]]]], "SEEK_SET", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6", "^7"]]]]], "size64", ["^2", [["^ ", "^3", ["^2", ["^9", "^4"]]]]], "truncate64", ["^2", [["^ ", "^3", ["^2", ["^9", "^4"]]]]], "read", ["^2", [["^ ", "^3", ["^2", ["^9", "^4"]]]]], "seek", ["^2", [["^ ", "^3", ["^2", ["^9", "^4"]]]]], "seek64", ["^2", [["^ ", "^3", ["^2", ["^9", "^4"]]]]], "getOID", ["^2", [["^ ", "^3", ["^2", ["^9", "^4"]]]]], "truncate", ["^2", [["^ ", "^3", ["^2", ["^9", "^4"]]]]], "close", ["^2", [["^ ", "^3", ["^2", ["^9", "^4"]]]]], "getOutputStream", ["^2", [["^ ", "^3", ["^2", ["^9", "^4"]]]]], "SEEK_CUR", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6", "^7"]]]]], "getInputStream", ["^2", [["^ ", "^3", ["^2", ["^9", "^4"]]]]], "size", ["^2", [["^ ", "^3", ["^2", ["^9", "^4"]]]]], "tell64", ["^2", [["^ ", "^3", ["^2", ["^9", "^4"]]]]], "write", ["^2", [["^ ", "^3", ["^2", ["^9", "^4"]]]]], "getLongOID", ["^2", [["^ ", "^3", ["^2", ["^9", "^4"]]]]]]]