["^ ", "~:members", ["^ ", "set<PERSON><PERSON><PERSON>", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "hasMoreElements", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "hasMoreTokens", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "get<PERSON>ingle", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "<init>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "nextToken", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isQuoted", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "~:static"]]]]], "setDouble", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "nextElement", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "quoteOnly", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^<"]]]]], "unquoteOnly", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^<"]]]]], "quote", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^<"]]]]], "unquote", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^<"]]]]], "getDouble", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "countTokens", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "quoteIfNeeded", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^<"]]]]]]]