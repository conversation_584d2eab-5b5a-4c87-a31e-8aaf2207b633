["^ ", "~:members", ["^ ", "<init>", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "getEvictionPolicy", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setEvictionPolicy", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isSaveOnInactiveEvict", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setSaveOnInactiveEvict", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isInvalidateOnShutdown", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setInvalidateOnShutdown", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "newSessionCache", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]