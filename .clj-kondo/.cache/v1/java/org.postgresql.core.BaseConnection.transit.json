["^ ", "~:members", ["^ ", "getReplicationProtocol", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "purgeTimerTasks", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getEncoding", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getTypeInfo", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "execSQLUpdate", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "haveMinimumServerVersion", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "addTimerTask", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getObject", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getFieldMetadataCache", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "encodeString", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "escapeString", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getXmlFactoryFactory", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "cancelQuery", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isColumnSanitiserDisabled", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "hintReadOnly", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getStringVarcharFlag", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getLogServerErrorDetail", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getStandardConformingStrings", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getTransactionState", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "execSQLQuery", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "createQuery", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "binaryTransferSend", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getQueryExecutor", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "<PERSON><PERSON><PERSON><PERSON>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setFlushCacheOnDeallocate", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getTimestampUtils", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]