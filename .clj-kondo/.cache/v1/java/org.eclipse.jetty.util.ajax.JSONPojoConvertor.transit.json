["^ ", "~:members", ["^ ", "fromJSON", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "NULL_ARG", ["^2", [["^ ", "^3", ["^2", ["^5", "~:static", "~:field", "~:final"]]]]], "<init>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "DOUBLE", ["^2", [["^ ", "^3", ["^2", ["^5", "^7", "^8", "^9"]]]]], "setProps", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "GETTER_ARG", ["^2", [["^ ", "^3", ["^2", ["^5", "^7", "^8", "^9"]]]]], "SHORT", ["^2", [["^ ", "^3", ["^2", ["^5", "^7", "^8", "^9"]]]]], "INTEGER", ["^2", [["^ ", "^3", ["^2", ["^5", "^7", "^8", "^9"]]]]], "LONG", ["^2", [["^ ", "^3", ["^2", ["^5", "^7", "^8", "^9"]]]]], "FLOAT", ["^2", [["^ ", "^3", ["^2", ["^5", "^7", "^8", "^9"]]]]], "getNumberType", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^7"]]]]], "toJSON", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]