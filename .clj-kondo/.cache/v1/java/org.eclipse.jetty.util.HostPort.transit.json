["^ ", "~:members", ["^ ", "<init>", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "getHost", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getPort", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "hasHost", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "has<PERSON>ort", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "toString", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "normalizeHost", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "~:static"]]]]], "parsePort", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^<"]]]]]]]