["^ ", "~:members", ["^ ", "setBinarySendOids", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "getParameterStatus", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isReWriteBatchedInsertsEnabled", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "removeBinarySendOid", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "fetch", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getReplicationProtocol", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "borrowReturningQuery", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "addBinarySendOid", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getEscapeSyntaxCallMode", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getBackendPID", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getPreferQueryMode", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getDatabase", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "fastpathCall", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getAdaptiveFetchSize", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "createQueryByKey", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "createQueryKey", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "QUERY_FORCE_DESCRIBE_PORTAL", ["^2", [["^ ", "^3", ["^2", ["^5", "~:static", "~:field", "~:final"]]]]], "getEncoding", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getParameterStatuses", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "QUERY_DESCRIBE_ONLY", ["^2", [["^ ", "^3", ["^2", ["^5", "^F", "^G", "^H"]]]]], "QUERY_DISALLOW_BATCHING", ["^2", [["^ ", "^3", ["^2", ["^5", "^F", "^G", "^H"]]]]], "<PERSON><PERSON><PERSON>y", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "QUERY_BOTH_ROWS_AND_STATUS", ["^2", [["^ ", "^3", ["^2", ["^5", "^F", "^G", "^H"]]]]], "MAX_SAVE_POINTS", ["^2", [["^ ", "^3", ["^2", ["^5", "^F", "^G", "^H"]]]]], "QUERY_READ_ONLY_HINT", ["^2", [["^ ", "^3", ["^2", ["^5", "^F", "^G", "^H"]]]]], "QUERY_NO_RESULTS", ["^2", [["^ ", "^3", ["^2", ["^5", "^F", "^G", "^H"]]]]], "removeBinaryReceiveOid", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getCloseAction", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "releaseQuery", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getApplicationName", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getBinaryReceiveOids", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getNotifications", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getIntegerDateTimes", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getWarnings", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "QUERY_NO_METADATA", ["^2", [["^ ", "^3", ["^2", ["^5", "^F", "^G", "^H"]]]]], "QUERY_EXECUTE_AS_SIMPLE", ["^2", [["^ ", "^3", ["^2", ["^5", "^F", "^G", "^H"]]]]], "QUERY_SUPPRESS_BEGIN", ["^2", [["^ ", "^3", ["^2", ["^5", "^F", "^G", "^H"]]]]], "addBinaryReceiveOid", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setAutoSave", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "wrap", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "addQueryToAdaptiveFetchCache", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "willHealOnRetry", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isClosed", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getUser", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "QUERY_NO_BINARY_TRANSFER", ["^2", [["^ ", "^3", ["^2", ["^5", "^F", "^G", "^H"]]]]], "getAutoSave", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getTimeZone", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "borrowCallableQuery", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setAdaptiveFetch", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isColumnSanitiserDisabled", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getStandardConformingStrings", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getTransactionState", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "close", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "createQuery", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getQuoteReturningIdentifiers", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getServerVersion", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "createSimpleQuery", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "execute", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getServerVersionNum", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getBinarySendOids", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "borrowQueryByKey", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "QUERY_ONESHOT", ["^2", [["^ ", "^3", ["^2", ["^5", "^F", "^G", "^H"]]]]], "createFastpathParameters", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "abort", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "QUERY_FORWARD_CURSOR", ["^2", [["^ ", "^3", ["^2", ["^5", "^F", "^G", "^H"]]]]], "getAdaptiveFetch", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setFlushCacheOnDeallocate", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "startCopy", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "sendQueryCancel", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "processNotifies", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setBinaryReceiveOids", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getHostSpec", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getNetworkTimeout", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "removeQueryFromAdaptiveFetchCache", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setNetworkTimeout", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getProtocolVersion", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]