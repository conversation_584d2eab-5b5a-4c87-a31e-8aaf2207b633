["^ ", "~:members", ["^ ", "onTextFrame", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "onPong", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "onClose", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "onTextMessage", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "onBinaryFrame", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "onConnect", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "onError", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "onPing", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getSession", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "onReader", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "onBinaryMessage", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "onFrame", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getPolicy", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "onContinuationFrame", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "onInputStream", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getBatchMode", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "openSession", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]