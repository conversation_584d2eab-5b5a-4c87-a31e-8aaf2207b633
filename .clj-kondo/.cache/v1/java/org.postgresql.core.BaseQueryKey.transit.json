["^ ", "~:members", ["^ ", "sql", ["~#set", [["^ ", "~:flags", ["^1", ["~:public", "~:field", "~:final"]]]]], "isParameterized", ["^1", [["^ ", "^2", ["^1", ["^3", "^4", "^5"]]]]], "escapeProcessing", ["^1", [["^ ", "^2", ["^1", ["^3", "^4", "^5"]]]]], "toString", ["^1", [["^ ", "^2", ["^1", ["~:method", "^3"]]]]], "getSize", ["^1", [["^ ", "^2", ["^1", ["^9", "^3"]]]]], "equals", ["^1", [["^ ", "^2", ["^1", ["^9", "^3"]]]]], "hashCode", ["^1", [["^ ", "^2", ["^1", ["^9", "^3"]]]]]]]