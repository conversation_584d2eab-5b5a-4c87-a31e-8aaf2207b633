["^ ", "~:members", ["^ ", "shortValue", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "doubleValue", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "longValue", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "byteValue", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "toString", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "bigIntegerValue", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "floatValue", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "writeTo", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "intValue", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "hashCode", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getFloat", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getDouble", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "equals", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]