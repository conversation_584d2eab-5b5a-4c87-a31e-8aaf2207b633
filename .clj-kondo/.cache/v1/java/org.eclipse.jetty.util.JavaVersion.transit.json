["^ ", "~:members", ["^ ", "get<PERSON>icro", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "getSuffix", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "toString", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getMinor", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "VERSION", ["^2", [["^ ", "^3", ["^2", ["^5", "~:static", "~:field", "~:final"]]]]], "getVersion", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getUpdate", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "parse", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^:"]]]]], "JAVA_TARGET_PLATFORM", ["^2", [["^ ", "^3", ["^2", ["^5", "^:", "^;", "^<"]]]]], "getPlatform", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "<PERSON><PERSON><PERSON><PERSON>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]