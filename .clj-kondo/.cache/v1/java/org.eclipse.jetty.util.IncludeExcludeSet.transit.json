["^ ", "~:members", ["^ ", "getIncluded", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "<init>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "toString", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "matchCombined", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "~:static"]]]]], "include", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isIncludedAndNotExcluded", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "hasIncludes", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "matches", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getExcluded", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "exclude", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isEmpty", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "hasExcludes", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "size", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "clear", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "test", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]