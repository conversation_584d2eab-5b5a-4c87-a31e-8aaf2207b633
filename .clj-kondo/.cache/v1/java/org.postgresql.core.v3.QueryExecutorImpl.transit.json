["^ ", "~:members", ["^ ", "setBinarySendOids", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "setTimeZone", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "removeBinarySendOid", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "fetch", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getReplicationProtocol", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "addBinarySendOid", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "fastpathCall", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getAdaptiveFetchSize", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "writeToCopy", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "removeBinaryReceiveOid", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "useBinaryForSend", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getApplicationName", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "doSubprotocolBegin", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getBinaryReceiveOids", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "<init>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getIntegerDateTimes", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "addBinaryReceiveOid", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "wrap", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "addQueryToAdaptiveFetchCache", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "useBinaryForReceive", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getTimeZone", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setAdaptiveFetch", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "flushCopy", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "createSimpleQuery", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "execute", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getBinarySendOids", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "readStartupMessages", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "createFastpathParameters", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "cancelCopy", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getAdaptiveFetch", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "startCopy", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "processNotifies", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setBinaryReceiveOids", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "removeQueryFromAdaptiveFetchCache", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "receiveParameterStatus", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setApplicationName", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "endCopy", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getProtocolVersion", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]