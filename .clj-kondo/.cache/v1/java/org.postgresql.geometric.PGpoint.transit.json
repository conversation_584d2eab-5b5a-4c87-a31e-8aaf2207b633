["^ ", "~:members", ["^ ", "setLocation", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "isNull", ["^2", [["^ ", "^3", ["^2", ["^5", "~:field"]]]]], "translate", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "x", ["^2", [["^ ", "^3", ["^2", ["^5", "^7"]]]]], "<init>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getValue", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setByteValue", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "move", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setValue", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "y", ["^2", [["^ ", "^3", ["^2", ["^5", "^7"]]]]], "lengthInBytes", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "clone", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "toBytes", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "hashCode", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "equals", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]