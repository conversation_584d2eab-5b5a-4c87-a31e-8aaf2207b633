["^ ", "~:members", ["^ ", "invalidateAll", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "getSessionHandlers", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "renewSessionId", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getSessionHouse<PERSON>eeper", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "newSessionId", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "expireAll", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getWorkerName", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isIdInUse", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getId", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getExtendedId", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setSessionHouse<PERSON><PERSON><PERSON>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]