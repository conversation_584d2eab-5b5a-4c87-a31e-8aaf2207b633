["^ ", "~:members", ["^ ", "free", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "getBaseTypeName", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "escapeArrayElement", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "~:static"]]]]], "<init>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "toString", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getBaseType", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getArray", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getResultSetImpl", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "toBytes", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isBinary", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getResultSet", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getArrayImpl", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]