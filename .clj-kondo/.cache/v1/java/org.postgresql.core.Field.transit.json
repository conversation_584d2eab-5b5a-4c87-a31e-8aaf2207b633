["^ ", "~:members", ["^ ", "getColumnLabel", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "get<PERSON>od", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setFormat", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "<init>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isTypeInitialized", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "toString", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setPGType", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getPGType", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getOID", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "BINARY_FORMAT", ["^2", [["^ ", "^3", ["^2", ["^5", "~:static", "~:field", "~:final"]]]]], "<PERSON><PERSON><PERSON><PERSON>", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getFormat", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getSQLType", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getPositionInTable", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getTableOid", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getMetadata", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "TEXT_FORMAT", ["^2", [["^ ", "^3", ["^2", ["^5", "^?", "^@", "^A"]]]]], "setSQLType", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "upperCaseLabel", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setMetadata", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]