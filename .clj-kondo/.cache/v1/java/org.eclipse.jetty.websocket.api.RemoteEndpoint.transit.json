["^ ", "~:members", ["^ ", "setMaxOutgoingFrames", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public"]]]]], "flush", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setBatchMode", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "sendPong", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getInetSocketAddress", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "sendPing", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "sendBytes", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "sendBytesByFuture", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "sendPartialString", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "sendStringByFuture", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "sendString", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getBatchMode", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "sendPartialBytes", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getMaxOutgoingFrames", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]