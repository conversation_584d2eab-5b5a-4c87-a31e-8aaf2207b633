["^ ", "~:members", ["^ ", "getParameterStatus", ["~#set", [["^ ", "~:flags", ["^2", ["~:method", "~:public", "~:final"]]]]], "isReWriteBatchedInsertsEnabled", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isFlushCacheOnDeallocate", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "borrowReturningQuery", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "getEscapeSyntaxCallMode", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getBackendPID", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getPreferQueryMode", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getDatabase", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "createQueryByKey", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "createQueryKey", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "getEncoding", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getParameterStatuses", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "<PERSON><PERSON><PERSON>y", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "setBackendKeyData", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getCloseAction", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "releaseQuery", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setEncoding", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getNotifications", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getWarnings", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setAutoSave", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "willHealOnRetry", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "isClosed", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getUser", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getAutoSave", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "borrowCallableQuery", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "isColumnSanitiserDisabled", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getStandardConformingStrings", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setTransactionState", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getTransactionState", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "close", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "createQuery", ["^2", [["^ ", "^3", ["^2", ["^4", "^5", "^6"]]]]], "addNotification", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setServerVersionNum", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getQuoteReturningIdentifiers", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setStandardConformingStrings", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getServerVersion", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getServerVersionNum", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "borrowQueryByKey", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "abort", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setServerVersion", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setFlushCacheOnDeallocate", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "sendQueryCancel", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "addWarning", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getHostSpec", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "getNetworkTimeout", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]], "setNetworkTimeout", ["^2", [["^ ", "^3", ["^2", ["^4", "^5"]]]]]]]