# Trade Finance BPMN Proof of Concept

This demonstrates how to use BPMN processes as "business rules" in a Clojure/Pedestal application, similar to how you'd use JSON Schema for data validation.

## Architecture

```
Invoice Request → Pedestal API → Flowable BPMN → Business Logic → Database
     ↓                ↓              ↓               ↓             ↓
 JSON Schema    Route Handler   Process Engine   External Tasks  SQLite
```

## Quick Start

### 1. Start Flowable Engine

```bash
# Start Flowable with PostgreSQL
docker-compose up -d

# Wait for startup (about 30 seconds)
docker-compose logs -f flowable-app
```

### 2. Deploy BPMN Process

1. Open Flowable UI: http://localhost:8080/flowable-ui
2. **Login credentials:**
   - Username: `admin`
   - Password: `test`
3. Click on **"Modeler App"**
4. Click **"Create Process"** or **"Import"**
5. Upload the `bpmn-models/invoice-discounting.bpmn` file
6. Save and deploy the process

**Alternative: Use Admin App**
1. Click on **"Admin App"**
2. Go to **"Process Engine"** → **"Deployments"**
3. Click **"Deploy new"** and upload the BPMN file

### 3. Start Clojure Application

```bash
# Install dependencies
clj -M:dev

# Start the server
clj -M -m trade-finance.core
```

Your API will be available at: http://localhost:8090

### 4. Test the Flow

```bash
# Submit an invoice
curl -X POST http://localhost:8090/api/invoice/submit \
  -H "Content-Type: application/json" \
  -d '{
    "client-id": "CLIENT-001",
    "invoice-number": "INV-2024-001", 
    "amount": 50000.00,
    "debtor-name": "ABC Corporation"
  }'

# Check status (use process-id from response)
curl http://localhost:8090/api/invoice/status/YOUR-PROCESS-ID
```

## Key Concepts Demonstrated

### 1. BPMN as Business Rules

Your BPMN model defines:
- ✅ What steps must be followed
- ✅ Who can approve what amounts
- ✅ What validations are required
- ✅ How errors are handled

### 2. External Task Pattern

Flowable calls your Clojure app for business logic:

```clojure
;; Your app implements the actual business rules
(defn check-client-eligibility [client-id amount]
  ;; Real implementation would check:
  ;; - Credit bureau
  ;; - Internal limits
  ;; - Regulatory compliance
  {:eligible true :reason "Within limits"})
```

### 3. Process-Driven Architecture

```clojure
;; Every invoice MUST go through the defined process
(defn submit-invoice [invoice-data]
  ;; Can't bypass the BPMN process
  (start-invoice-process invoice-data))
```

### 4. Audit Trail

Every step is automatically logged:
- Who did what
- When decisions were made
- What data was used
- Why processes failed

## Database Schema

```sql
-- Process state storage
CREATE TABLE invoice_requests (
  id INTEGER PRIMARY KEY,
  process_instance_id TEXT UNIQUE,
  client_id TEXT,
  status TEXT,
  created_at TIMESTAMP
);

-- Complete audit trail
CREATE TABLE process_events (
  id INTEGER PRIMARY KEY,
  process_instance_id TEXT,
  event_type TEXT,
  event_data TEXT,
  timestamp TIMESTAMP
);
```

## API Endpoints

### Business APIs
- `POST /api/invoice/submit` - Submit invoice for processing
- `GET /api/invoice/status/:process-id` - Check processing status

### External Task APIs (called by Flowable)
- `POST /api/tasks/check-eligibility` - Client eligibility check
- `POST /api/tasks/verify-debtor` - Debtor verification
- `POST /api/tasks/calculate-discount` - Discount rate calculation

## Benefits of This Approach

### 1. **Compliance by Design**
- No code can bypass defined processes
- Regulatory requirements are enforced in BPMN
- Audit trails are automatic

### 2. **Business Visibility**
- Non-technical staff can see and modify processes
- Process performance metrics are built-in
- Clear escalation paths

### 3. **Flexibility**
- Change processes without code deployment
- A/B test different approval workflows
- Handle exceptions gracefully

### 4. **Integration Ready**
- External systems integrate via REST/messaging
- Microservices architecture friendly
- Cloud-native deployment options

## Next Steps

1. **Add More Business Rules**: Credit scoring, regulatory checks, etc.
2. **Implement User Tasks**: Manual approval workflows
3. **Add Monitoring**: Process dashboards and alerts
4. **Scale Up**: Multiple process instances, clustering
5. **Production Setup**: Security, monitoring, deployment

## Production Considerations

- Use PostgreSQL instead of SQLite
- Implement proper authentication
- Add monitoring and alerting
- Set up process governance
- Consider Camunda Platform for enterprise features

This PoC shows how BPMN can serve as your "business schema" - ensuring all trade finance operations follow defined, auditable processes while keeping your Clojure application clean and focused on business logic.
