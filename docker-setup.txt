# docker-compose.yml
version: '3.8'

services:
  flowable-db:
    image: postgres:13
    environment:
      POSTGRES_DB: flowable
      POSTGRES_USER: flowable
      POSTGRES_PASSWORD: flowable
    volumes:
      - flowable_db_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - flowable-network

  flowable-app:
    image: flowable/flowable-ui:7.0.0
    depends_on:
      - flowable-db
    environment:
      - FLOWABLE_DATABASE_URL=*******************************************
      - FLOWABLE_DATABASE_USERNAME=flowable
      - FLOWABLE_DATABASE_PASSWORD=flowable
      - FLOWABLE_DATABASE_DRIVER=org.postgresql.Driver
    ports:
      - "8080:8080"
    networks:
      - flowable-network
    volumes:
      - ./bpmn-models:/opt/flowable/models
    
  # Alternative: Standalone Flowable REST API only
  flowable-rest:
    image: flowable/flowable-rest:7.0.0
    depends_on:
      - flowable-db
    environment:
      - FLOWABLE_DATABASE_URL=*******************************************
      - FLOWABLE_DATABASE_USERNAME=flowable
      - FLOWABLE_DATABASE_PASSWORD=flowable
      - FLOWABLE_DATABASE_DRIVER=org.postgresql.Driver
    ports:
      - "8081:8080"
    networks:
      - flowable-network

volumes:
  flowable_db_data:

networks:
  flowable-network:
    driver: bridge
