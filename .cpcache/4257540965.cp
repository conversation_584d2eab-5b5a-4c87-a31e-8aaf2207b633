src:resources:/home/<USER>/.m2/repository/clj-http/clj-http/3.12.3/clj-http-3.12.3.jar:/home/<USER>/.m2/repository/clojure/java-time/clojure.java-time/1.4.2/clojure.java-time-1.4.2.jar:/home/<USER>/.m2/repository/com/github/seancorfield/next.jdbc/1.3.909/next.jdbc-1.3.909.jar:/home/<USER>/.m2/repository/io/pedestal/pedestal.jetty/0.6.3/pedestal.jetty-0.6.3.jar:/home/<USER>/.m2/repository/io/pedestal/pedestal.service/0.6.3/pedestal.service-0.6.3.jar:/home/<USER>/.m2/repository/org/clojure/clojure/1.11.1/clojure-1.11.1.jar:/home/<USER>/.m2/repository/org/clojure/data.json/2.4.0/data.json-2.4.0.jar:/home/<USER>/.m2/repository/org/clojure/tools.logging/1.2.4/tools.logging-1.2.4.jar:/home/<USER>/.m2/repository/org/xerial/sqlite-jdbc/3.44.1.0/sqlite-jdbc-3.44.1.0.jar:/home/<USER>/.m2/repository/prismatic/schema/1.4.1/schema-1.4.1.jar:/home/<USER>/.m2/repository/commons-codec/commons-codec/1.15/commons-codec-1.15.jar:/home/<USER>/.m2/repository/org/apache/httpcomponents/httpasyncclient/4.1.4/httpasyncclient-4.1.4.jar:/home/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.13/httpclient-4.5.13.jar:/home/<USER>/.m2/repository/org/apache/httpcomponents/httpclient-cache/4.5.13/httpclient-cache-4.5.13.jar:/home/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.14/httpcore-4.4.14.jar:/home/<USER>/.m2/repository/org/apache/httpcomponents/httpmime/4.5.13/httpmime-4.5.13.jar:/home/<USER>/.m2/repository/potemkin/potemkin/0.4.5/potemkin-0.4.5.jar:/home/<USER>/.m2/repository/slingshot/slingshot/0.12.2/slingshot-0.12.2.jar:/home/<USER>/.m2/repository/camel-snake-kebab/camel-snake-kebab/0.4.3/camel-snake-kebab-0.4.3.jar:/home/<USER>/.m2/repository/org/clojure/java.data/1.1.103/java.data-1.1.103.jar:/home/<USER>/.m2/repository/io/pedestal/pedestal.log/0.6.3/pedestal.log-0.6.3.jar:/home/<USER>/.m2/repository/javax/servlet/javax.servlet-api/3.1.0/javax.servlet-api-3.1.0.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-alpn-server/9.4.53.v20231009/jetty-alpn-server-9.4.53.v20231009.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-server/9.4.53.v20231009/jetty-server-9.4.53.v20231009.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-servlet/9.4.53.v20231009/jetty-servlet-9.4.53.v20231009.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/alpn/alpn-api/1.1.3.v20160715/alpn-api-1.1.3.v20160715.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/http2/http2-server/9.4.53.v20231009/http2-server-9.4.53.v20231009.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/websocket/websocket-api/9.4.53.v20231009/websocket-api-9.4.53.v20231009.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/websocket/websocket-server/9.4.53.v20231009/websocket-server-9.4.53.v20231009.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/websocket/websocket-servlet/9.4.53.v20231009/websocket-servlet-9.4.53.v20231009.jar:/home/<USER>/.m2/repository/cheshire/cheshire/5.11.0/cheshire-5.11.0.jar:/home/<USER>/.m2/repository/com/cognitect/transit-clj/1.0.329/transit-clj-1.0.329.jar:/home/<USER>/.m2/repository/crypto-equality/crypto-equality/1.0.1/crypto-equality-1.0.1.jar:/home/<USER>/.m2/repository/crypto-random/crypto-random/1.2.1/crypto-random-1.2.1.jar:/home/<USER>/.m2/repository/io/pedestal/pedestal.interceptor/0.6.3/pedestal.interceptor-0.6.3.jar:/home/<USER>/.m2/repository/io/pedestal/pedestal.route/0.6.3/pedestal.route-0.6.3.jar:/home/<USER>/.m2/repository/org/clojure/core.async/1.6.673/core.async-1.6.673.jar:/home/<USER>/.m2/repository/org/clojure/tools.reader/1.3.6/tools.reader-1.3.6.jar:/home/<USER>/.m2/repository/ring/ring-core/1.10.0/ring-core-1.10.0.jar:/home/<USER>/.m2/repository/org/clojure/core.specs.alpha/0.2.62/core.specs.alpha-0.2.62.jar:/home/<USER>/.m2/repository/org/clojure/spec.alpha/0.3.218/spec.alpha-0.3.218.jar:/home/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar:/home/<USER>/.m2/repository/commons-logging/commons-logging/1.2/commons-logging-1.2.jar:/home/<USER>/.m2/repository/org/apache/httpcomponents/httpcore-nio/4.4.10/httpcore-nio-4.4.10.jar:/home/<USER>/.m2/repository/clj-tuple/clj-tuple/0.2.2/clj-tuple-0.2.2.jar:/home/<USER>/.m2/repository/riddley/riddley/0.1.12/riddley-0.1.12.jar:/home/<USER>/.m2/repository/io/dropwizard/metrics/metrics-core/4.2.18/metrics-core-4.2.18.jar:/home/<USER>/.m2/repository/io/dropwizard/metrics/metrics-jmx/4.2.18/metrics-jmx-4.2.18.jar:/home/<USER>/.m2/repository/io/opentracing/opentracing-api/0.33.0/opentracing-api-0.33.0.jar:/home/<USER>/.m2/repository/io/opentracing/opentracing-util/0.33.0/opentracing-util-0.33.0.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-http/9.4.53.v20231009/jetty-http-9.4.53.v20231009.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-io/9.4.53.v20231009/jetty-io-9.4.53.v20231009.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-security/9.4.53.v20231009/jetty-security-9.4.53.v20231009.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-util-ajax/9.4.53.v20231009/jetty-util-ajax-9.4.53.v20231009.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/http2/http2-common/9.4.53.v20231009/http2-common-9.4.53.v20231009.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/websocket/websocket-client/9.4.53.v20231009/websocket-client-9.4.53.v20231009.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/websocket/websocket-common/9.4.53.v20231009/websocket-common-9.4.53.v20231009.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.13.3/jackson-core-2.13.3.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-cbor/2.13.3/jackson-dataformat-cbor-2.13.3.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-smile/2.13.3/jackson-dataformat-smile-2.13.3.jar:/home/<USER>/.m2/repository/tigris/tigris/0.1.2/tigris-0.1.2.jar:/home/<USER>/.m2/repository/com/cognitect/transit-java/1.0.362/transit-java-1.0.362.jar:/home/<USER>/.m2/repository/org/clojure/core.match/1.0.1/core.match-1.0.1.jar:/home/<USER>/.m2/repository/org/clojure/tools.analyzer.jvm/1.2.2/tools.analyzer.jvm-1.2.2.jar:/home/<USER>/.m2/repository/commons-fileupload/commons-fileupload/1.5/commons-fileupload-1.5.jar:/home/<USER>/.m2/repository/commons-io/commons-io/2.11.0/commons-io-2.11.0.jar:/home/<USER>/.m2/repository/ring/ring-codec/1.2.0/ring-codec-1.2.0.jar:/home/<USER>/.m2/repository/io/opentracing/opentracing-noop/0.33.0/opentracing-noop-0.33.0.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-util/9.4.53.v20231009/jetty-util-9.4.53.v20231009.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/http2/http2-hpack/9.4.53.v20231009/http2-hpack-9.4.53.v20231009.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-client/9.4.53.v20231009/jetty-client-9.4.53.v20231009.jar:/home/<USER>/.m2/repository/javax/xml/bind/jaxb-api/2.3.0/jaxb-api-2.3.0.jar:/home/<USER>/.m2/repository/org/msgpack/msgpack/0.6.12/msgpack-0.6.12.jar:/home/<USER>/.m2/repository/org/clojure/core.memoize/1.0.253/core.memoize-1.0.253.jar:/home/<USER>/.m2/repository/org/clojure/tools.analyzer/1.1.0/tools.analyzer-1.1.0.jar:/home/<USER>/.m2/repository/org/ow2/asm/asm/9.2/asm-9.2.jar:/home/<USER>/.m2/repository/com/googlecode/json-simple/json-simple/1.1.1/json-simple-1.1.1.jar:/home/<USER>/.m2/repository/org/javassist/javassist/3.18.1-GA/javassist-3.18.1-GA.jar:/home/<USER>/.m2/repository/org/clojure/core.cache/1.0.225/core.cache-1.0.225.jar:/home/<USER>/.m2/repository/org/clojure/data.priority-map/1.1.0/data.priority-map-1.1.0.jar