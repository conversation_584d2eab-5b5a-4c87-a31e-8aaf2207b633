{:paths ["src" "resources"], :deps {org.clojure/data.json {:mvn/version "2.4.0"}, org.clojure/clojure {:mvn/version "1.11.1"}, org.clojure/tools.logging {:mvn/version "1.2.4"}, io.pedestal/pedestal.jetty {:mvn/version "0.6.3"}, io.pedestal/pedestal.service {:mvn/version "0.6.3"}, clj-http/clj-http {:mvn/version "3.12.3"}, clojure.java-time/clojure.java-time {:mvn/version "1.4.2"}, com.github.seancorfield/next.jdbc {:mvn/version "1.3.909"}, prismatic/schema {:mvn/version "1.4.1"}, org.xerial/sqlite-jdbc {:mvn/version "********"}}, :aliases {:deps {:replace-paths [], :replace-deps {org.clojure/tools.deps.cli {:mvn/version "0.11.72"}}, :ns-default clojure.tools.deps.cli.api, :ns-aliases {help clojure.tools.deps.cli.help}}, :test {:extra-paths ["test"], :extra-deps {org.clojure/test.check {:mvn/version "1.1.1"}}}, :dev {:extra-deps {org.clojure/tools.namespace {:mvn/version "1.4.4"}}}, :flowable {:extra-deps {org.flowable/flowable-engine {:mvn/version "7.0.0"}, org.flowable/flowable-spring-boot-starter {:mvn/version "7.0.0"}}}}, :mvn/repos {"central" {:url "https://repo1.maven.org/maven2/"}, "clojars" {:url "https://repo.clojars.org/"}}, :libs {com.cognitect/transit-java {:mvn/version "1.0.362", :deps/manifest :mvn, :dependents [com.cognitect/transit-clj], :parents #{[io.pedestal/pedestal.service com.cognitect/transit-clj]}, :paths ["/home/<USER>/.m2/repository/com/cognitect/transit-java/1.0.362/transit-java-1.0.362.jar"]}, org.clojure/data.json {:mvn/version "2.4.0", :deps/manifest :mvn, :parents #{[]}, :paths ["/home/<USER>/.m2/repository/org/clojure/data.json/2.4.0/data.json-2.4.0.jar"]}, org.clojure/clojure {:mvn/version "1.11.1", :deps/manifest :mvn, :parents #{[]}, :paths ["/home/<USER>/.m2/repository/org/clojure/clojure/1.11.1/clojure-1.11.1.jar"]}, commons-codec/commons-codec {:mvn/version "1.15", :deps/manifest :mvn, :dependents [io.pedestal/pedestal.service clj-http/clj-http crypto-random/crypto-random], :parents #{[io.pedestal/pedestal.service] [clj-http/clj-http] [io.pedestal/pedestal.service crypto-random/crypto-random]}, :paths ["/home/<USER>/.m2/repository/commons-codec/commons-codec/1.15/commons-codec-1.15.jar"]}, org.clojure/tools.analyzer {:mvn/version "1.1.0", :deps/manifest :mvn, :dependents [org.clojure/tools.analyzer.jvm], :parents #{[io.pedestal/pedestal.service org.clojure/core.async org.clojure/tools.analyzer.jvm]}, :paths ["/home/<USER>/.m2/repository/org/clojure/tools.analyzer/1.1.0/tools.analyzer-1.1.0.jar"]}, org.eclipse.jetty/jetty-servlet {:mvn/version "9.4.53.v20231009", :deps/manifest :mvn, :dependents [org.eclipse.jetty.websocket/websocket-server io.pedestal/pedestal.jetty], :parents #{[io.pedestal/pedestal.jetty org.eclipse.jetty.websocket/websocket-server] [io.pedestal/pedestal.jetty]}, :paths ["/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-servlet/9.4.53.v20231009/jetty-servlet-9.4.53.v20231009.jar"]}, org.clojure/tools.logging {:mvn/version "1.2.4", :deps/manifest :mvn, :parents #{[]}, :paths ["/home/<USER>/.m2/repository/org/clojure/tools.logging/1.2.4/tools.logging-1.2.4.jar"]}, org.clojure/core.specs.alpha {:mvn/version "0.2.62", :deps/manifest :mvn, :dependents [org.clojure/clojure], :parents #{[org.clojure/clojure]}, :paths ["/home/<USER>/.m2/repository/org/clojure/core.specs.alpha/0.2.62/core.specs.alpha-0.2.62.jar"]}, io.pedestal/pedestal.jetty {:mvn/version "0.6.3", :deps/manifest :mvn, :parents #{[]}, :paths ["/home/<USER>/.m2/repository/io/pedestal/pedestal.jetty/0.6.3/pedestal.jetty-0.6.3.jar"]}, org.clojure/spec.alpha {:mvn/version "0.3.218", :deps/manifest :mvn, :dependents [org.clojure/clojure], :parents #{[org.clojure/clojure]}, :paths ["/home/<USER>/.m2/repository/org/clojure/spec.alpha/0.3.218/spec.alpha-0.3.218.jar"]}, org.eclipse.jetty.http2/http2-server {:mvn/version "9.4.53.v20231009", :deps/manifest :mvn, :dependents [io.pedestal/pedestal.jetty], :parents #{[io.pedestal/pedestal.jetty]}, :paths ["/home/<USER>/.m2/repository/org/eclipse/jetty/http2/http2-server/9.4.53.v20231009/http2-server-9.4.53.v20231009.jar"]}, commons-fileupload/commons-fileupload {:mvn/version "1.5", :deps/manifest :mvn, :dependents [ring/ring-core], :parents #{[io.pedestal/pedestal.service ring/ring-core]}, :paths ["/home/<USER>/.m2/repository/commons-fileupload/commons-fileupload/1.5/commons-fileupload-1.5.jar"]}, org.eclipse.jetty/jetty-http {:mvn/version "9.4.53.v20231009", :deps/manifest :mvn, :dependents [org.eclipse.jetty.http2/http2-hpack org.eclipse.jetty/jetty-server org.eclipse.jetty.websocket/websocket-server org.eclipse.jetty/jetty-client], :parents #{[io.pedestal/pedestal.jetty org.eclipse.jetty.http2/http2-server org.eclipse.jetty.http2/http2-common org.eclipse.jetty.http2/http2-hpack] [io.pedestal/pedestal.jetty org.eclipse.jetty/jetty-server] [io.pedestal/pedestal.jetty org.eclipse.jetty.websocket/websocket-server] [io.pedestal/pedestal.jetty org.eclipse.jetty.websocket/websocket-server org.eclipse.jetty.websocket/websocket-client org.eclipse.jetty/jetty-client]}, :paths ["/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-http/9.4.53.v20231009/jetty-http-9.4.53.v20231009.jar"]}, org.eclipse.jetty/jetty-util {:mvn/version "9.4.53.v20231009", :deps/manifest :mvn, :dependents [org.eclipse.jetty.http2/http2-hpack org.eclipse.jetty.websocket/websocket-client org.eclipse.jetty/jetty-util-ajax org.eclipse.jetty.websocket/websocket-common org.eclipse.jetty/jetty-http org.eclipse.jetty/jetty-io], :parents #{[io.pedestal/pedestal.jetty org.eclipse.jetty.http2/http2-server org.eclipse.jetty.http2/http2-common org.eclipse.jetty.http2/http2-hpack] [io.pedestal/pedestal.jetty org.eclipse.jetty.websocket/websocket-server org.eclipse.jetty.websocket/websocket-client] [io.pedestal/pedestal.jetty org.eclipse.jetty/jetty-servlet org.eclipse.jetty/jetty-util-ajax] [io.pedestal/pedestal.jetty org.eclipse.jetty.websocket/websocket-server org.eclipse.jetty.websocket/websocket-common] [io.pedestal/pedestal.jetty org.eclipse.jetty.websocket/websocket-server org.eclipse.jetty/jetty-http] [io.pedestal/pedestal.jetty org.eclipse.jetty/jetty-server org.eclipse.jetty/jetty-io]}, :paths ["/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-util/9.4.53.v20231009/jetty-util-9.4.53.v20231009.jar"]}, org.apache.httpcomponents/httpasyncclient {:mvn/version "4.1.4", :exclusions #{org.clojure/clojure}, :deps/manifest :mvn, :dependents [clj-http/clj-http], :parents #{[clj-http/clj-http]}, :paths ["/home/<USER>/.m2/repository/org/apache/httpcomponents/httpasyncclient/4.1.4/httpasyncclient-4.1.4.jar"]}, org.clojure/tools.analyzer.jvm {:mvn/version "1.2.2", :deps/manifest :mvn, :dependents [org.clojure/core.async], :parents #{[io.pedestal/pedestal.service org.clojure/core.async]}, :paths ["/home/<USER>/.m2/repository/org/clojure/tools.analyzer.jvm/1.2.2/tools.analyzer.jvm-1.2.2.jar"]}, io.pedestal/pedestal.log {:mvn/version "0.6.3", :deps/manifest :mvn, :dependents [io.pedestal/pedestal.service io.pedestal/pedestal.interceptor io.pedestal/pedestal.route io.pedestal/pedestal.jetty], :parents #{[io.pedestal/pedestal.service] [io.pedestal/pedestal.service io.pedestal/pedestal.interceptor] [io.pedestal/pedestal.service io.pedestal/pedestal.route] [io.pedestal/pedestal.jetty]}, :paths ["/home/<USER>/.m2/repository/io/pedestal/pedestal.log/0.6.3/pedestal.log-0.6.3.jar"]}, com.fasterxml.jackson.dataformat/jackson-dataformat-cbor {:mvn/version "2.13.3", :exclusions #{com.fasterxml.jackson.core/jackson-databind}, :deps/manifest :mvn, :dependents [cheshire/cheshire], :parents #{[io.pedestal/pedestal.service cheshire/cheshire]}, :paths ["/home/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-cbor/2.13.3/jackson-dataformat-cbor-2.13.3.jar"]}, io.pedestal/pedestal.service {:mvn/version "0.6.3", :deps/manifest :mvn, :parents #{[]}, :paths ["/home/<USER>/.m2/repository/io/pedestal/pedestal.service/0.6.3/pedestal.service-0.6.3.jar"]}, com.googlecode.json-simple/json-simple {:mvn/version "1.1.1", :exclusions #{junit/junit}, :deps/manifest :mvn, :dependents [org.msgpack/msgpack], :parents #{[io.pedestal/pedestal.service com.cognitect/transit-clj com.cognitect/transit-java org.msgpack/msgpack]}, :paths ["/home/<USER>/.m2/repository/com/googlecode/json-simple/json-simple/1.1.1/json-simple-1.1.1.jar"]}, slingshot/slingshot {:mvn/version "0.12.2", :exclusions #{org.clojure/clojure}, :deps/manifest :mvn, :dependents [clj-http/clj-http], :parents #{[clj-http/clj-http]}, :paths ["/home/<USER>/.m2/repository/slingshot/slingshot/0.12.2/slingshot-0.12.2.jar"]}, org.apache.httpcomponents/httpcore-nio {:mvn/version "4.4.10", :deps/manifest :mvn, :dependents [org.apache.httpcomponents/httpasyncclient], :parents #{[clj-http/clj-http org.apache.httpcomponents/httpasyncclient]}, :paths ["/home/<USER>/.m2/repository/org/apache/httpcomponents/httpcore-nio/4.4.10/httpcore-nio-4.4.10.jar"]}, org.eclipse.jetty.websocket/websocket-api {:mvn/version "9.4.53.v20231009", :deps/manifest :mvn, :dependents [org.eclipse.jetty.websocket/websocket-servlet org.eclipse.jetty.websocket/websocket-common io.pedestal/pedestal.jetty], :parents #{[io.pedestal/pedestal.jetty org.eclipse.jetty.websocket/websocket-servlet] [io.pedestal/pedestal.jetty org.eclipse.jetty.websocket/websocket-server org.eclipse.jetty.websocket/websocket-common] [io.pedestal/pedestal.jetty]}, :paths ["/home/<USER>/.m2/repository/org/eclipse/jetty/websocket/websocket-api/9.4.53.v20231009/websocket-api-9.4.53.v20231009.jar"]}, org.eclipse.jetty.alpn/alpn-api {:mvn/version "1.1.3.v20160715", :deps/manifest :mvn, :dependents [io.pedestal/pedestal.jetty], :parents #{[io.pedestal/pedestal.jetty]}, :paths ["/home/<USER>/.m2/repository/org/eclipse/jetty/alpn/alpn-api/1.1.3.v20160715/alpn-api-1.1.3.v20160715.jar"]}, commons-io/commons-io {:mvn/version "2.11.0", :deps/manifest :mvn, :dependents [commons-fileupload/commons-fileupload ring/ring-core], :parents #{[io.pedestal/pedestal.service ring/ring-core commons-fileupload/commons-fileupload] [io.pedestal/pedestal.service ring/ring-core]}, :paths ["/home/<USER>/.m2/repository/commons-io/commons-io/2.11.0/commons-io-2.11.0.jar"]}, org.eclipse.jetty.http2/http2-hpack {:mvn/version "9.4.53.v20231009", :deps/manifest :mvn, :dependents [org.eclipse.jetty.http2/http2-common], :parents #{[io.pedestal/pedestal.jetty org.eclipse.jetty.http2/http2-server org.eclipse.jetty.http2/http2-common]}, :paths ["/home/<USER>/.m2/repository/org/eclipse/jetty/http2/http2-hpack/9.4.53.v20231009/http2-hpack-9.4.53.v20231009.jar"]}, org.eclipse.jetty.websocket/websocket-client {:mvn/version "9.4.53.v20231009", :deps/manifest :mvn, :dependents [org.eclipse.jetty.websocket/websocket-server], :parents #{[io.pedestal/pedestal.jetty org.eclipse.jetty.websocket/websocket-server]}, :paths ["/home/<USER>/.m2/repository/org/eclipse/jetty/websocket/websocket-client/9.4.53.v20231009/websocket-client-9.4.53.v20231009.jar"]}, com.fasterxml.jackson.core/jackson-core {:mvn/version "2.13.3", :deps/manifest :mvn, :dependents [com.fasterxml.jackson.dataformat/jackson-dataformat-smile com.fasterxml.jackson.dataformat/jackson-dataformat-cbor cheshire/cheshire], :parents #{[io.pedestal/pedestal.service cheshire/cheshire com.fasterxml.jackson.dataformat/jackson-dataformat-smile] [io.pedestal/pedestal.service cheshire/cheshire com.fasterxml.jackson.dataformat/jackson-dataformat-cbor] [io.pedestal/pedestal.service cheshire/cheshire]}, :paths ["/home/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.13.3/jackson-core-2.13.3.jar"]}, org.eclipse.jetty.websocket/websocket-servlet {:mvn/version "9.4.53.v20231009", :deps/manifest :mvn, :dependents [org.eclipse.jetty.websocket/websocket-server io.pedestal/pedestal.jetty], :parents #{[io.pedestal/pedestal.jetty org.eclipse.jetty.websocket/websocket-server] [io.pedestal/pedestal.jetty]}, :paths ["/home/<USER>/.m2/repository/org/eclipse/jetty/websocket/websocket-servlet/9.4.53.v20231009/websocket-servlet-9.4.53.v20231009.jar"]}, clj-http/clj-http {:mvn/version "3.12.3", :deps/manifest :mvn, :parents #{[]}, :paths ["/home/<USER>/.m2/repository/clj-http/clj-http/3.12.3/clj-http-3.12.3.jar"]}, org.ow2.asm/asm {:mvn/version "9.2", :deps/manifest :mvn, :dependents [org.clojure/tools.analyzer.jvm], :parents #{[io.pedestal/pedestal.service org.clojure/core.async org.clojure/tools.analyzer.jvm]}, :paths ["/home/<USER>/.m2/repository/org/ow2/asm/asm/9.2/asm-9.2.jar"]}, org.apache.httpcomponents/httpcore {:mvn/version "4.4.14", :exclusions #{org.clojure/clojure}, :deps/manifest :mvn, :dependents [clj-http/clj-http], :parents #{[clj-http/clj-http]}, :paths ["/home/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.14/httpcore-4.4.14.jar"]}, org.eclipse.jetty/jetty-security {:mvn/version "9.4.53.v20231009", :deps/manifest :mvn, :dependents [org.eclipse.jetty/jetty-servlet], :parents #{[io.pedestal/pedestal.jetty org.eclipse.jetty/jetty-servlet]}, :paths ["/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-security/9.4.53.v20231009/jetty-security-9.4.53.v20231009.jar"]}, org.apache.httpcomponents/httpclient-cache {:mvn/version "4.5.13", :exclusions #{org.clojure/clojure}, :deps/manifest :mvn, :dependents [clj-http/clj-http], :parents #{[clj-http/clj-http]}, :paths ["/home/<USER>/.m2/repository/org/apache/httpcomponents/httpclient-cache/4.5.13/httpclient-cache-4.5.13.jar"]}, io.pedestal/pedestal.interceptor {:mvn/version "0.6.3", :deps/manifest :mvn, :dependents [io.pedestal/pedestal.service io.pedestal/pedestal.route], :parents #{[io.pedestal/pedestal.service] [io.pedestal/pedestal.service io.pedestal/pedestal.route]}, :paths ["/home/<USER>/.m2/repository/io/pedestal/pedestal.interceptor/0.6.3/pedestal.interceptor-0.6.3.jar"]}, io.dropwizard.metrics/metrics-core {:mvn/version "4.2.18", :deps/manifest :mvn, :dependents [io.pedestal/pedestal.log io.dropwizard.metrics/metrics-jmx], :parents #{[io.pedestal/pedestal.jetty io.pedestal/pedestal.log] [io.pedestal/pedestal.jetty io.pedestal/pedestal.log io.dropwizard.metrics/metrics-jmx]}, :paths ["/home/<USER>/.m2/repository/io/dropwizard/metrics/metrics-core/4.2.18/metrics-core-4.2.18.jar"]}, org.eclipse.jetty/jetty-util-ajax {:mvn/version "9.4.53.v20231009", :deps/manifest :mvn, :dependents [org.eclipse.jetty/jetty-servlet], :parents #{[io.pedestal/pedestal.jetty org.eclipse.jetty/jetty-servlet]}, :paths ["/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-util-ajax/9.4.53.v20231009/jetty-util-ajax-9.4.53.v20231009.jar"]}, clj-tuple/clj-tuple {:mvn/version "0.2.2", :deps/manifest :mvn, :dependents [potemkin/potemkin], :parents #{[clj-http/clj-http potemkin/potemkin]}, :paths ["/home/<USER>/.m2/repository/clj-tuple/clj-tuple/0.2.2/clj-tuple-0.2.2.jar"]}, riddley/riddley {:mvn/version "0.1.12", :deps/manifest :mvn, :dependents [potemkin/potemkin], :parents #{[clj-http/clj-http potemkin/potemkin]}, :paths ["/home/<USER>/.m2/repository/riddley/riddley/0.1.12/riddley-0.1.12.jar"]}, io.opentracing/opentracing-api {:mvn/version "0.33.0", :deps/manifest :mvn, :dependents [io.opentracing/opentracing-noop io.opentracing/opentracing-util io.pedestal/pedestal.log], :parents #{[io.pedestal/pedestal.jetty io.pedestal/pedestal.log io.opentracing/opentracing-util io.opentracing/opentracing-noop] [io.pedestal/pedestal.jetty io.pedestal/pedestal.log io.opentracing/opentracing-util] [io.pedestal/pedestal.jetty io.pedestal/pedestal.log]}, :paths ["/home/<USER>/.m2/repository/io/opentracing/opentracing-api/0.33.0/opentracing-api-0.33.0.jar"]}, org.javassist/javassist {:mvn/version "3.18.1-GA", :deps/manifest :mvn, :dependents [org.msgpack/msgpack], :parents #{[io.pedestal/pedestal.service com.cognitect/transit-clj com.cognitect/transit-java org.msgpack/msgpack]}, :paths ["/home/<USER>/.m2/repository/org/javassist/javassist/3.18.1-GA/javassist-3.18.1-GA.jar"]}, commons-logging/commons-logging {:mvn/version "1.2", :deps/manifest :mvn, :dependents [org.apache.httpcomponents/httpasyncclient org.apache.httpcomponents/httpclient org.apache.httpcomponents/httpclient-cache], :parents #{[clj-http/clj-http org.apache.httpcomponents/httpasyncclient] [clj-http/clj-http org.apache.httpcomponents/httpclient] [clj-http/clj-http org.apache.httpcomponents/httpclient-cache]}, :paths ["/home/<USER>/.m2/repository/commons-logging/commons-logging/1.2/commons-logging-1.2.jar"]}, clojure.java-time/clojure.java-time {:mvn/version "1.4.2", :deps/manifest :mvn, :parents #{[]}, :paths ["/home/<USER>/.m2/repository/clojure/java-time/clojure.java-time/1.4.2/clojure.java-time-1.4.2.jar"]}, org.eclipse.jetty.http2/http2-common {:mvn/version "9.4.53.v20231009", :deps/manifest :mvn, :dependents [org.eclipse.jetty.http2/http2-server], :parents #{[io.pedestal/pedestal.jetty org.eclipse.jetty.http2/http2-server]}, :paths ["/home/<USER>/.m2/repository/org/eclipse/jetty/http2/http2-common/9.4.53.v20231009/http2-common-9.4.53.v20231009.jar"]}, org.msgpack/msgpack {:mvn/version "0.6.12", :deps/manifest :mvn, :dependents [com.cognitect/transit-java], :parents #{[io.pedestal/pedestal.service com.cognitect/transit-clj com.cognitect/transit-java]}, :paths ["/home/<USER>/.m2/repository/org/msgpack/msgpack/0.6.12/msgpack-0.6.12.jar"]}, com.cognitect/transit-clj {:mvn/version "1.0.329", :deps/manifest :mvn, :dependents [io.pedestal/pedestal.service], :parents #{[io.pedestal/pedestal.service]}, :paths ["/home/<USER>/.m2/repository/com/cognitect/transit-clj/1.0.329/transit-clj-1.0.329.jar"]}, com.github.seancorfield/next.jdbc {:mvn/version "1.3.909", :deps/manifest :mvn, :parents #{[]}, :paths ["/home/<USER>/.m2/repository/com/github/seancorfield/next.jdbc/1.3.909/next.jdbc-1.3.909.jar"]}, io.opentracing/opentracing-noop {:mvn/version "0.33.0", :deps/manifest :mvn, :dependents [io.opentracing/opentracing-util], :parents #{[io.pedestal/pedestal.jetty io.pedestal/pedestal.log io.opentracing/opentracing-util]}, :paths ["/home/<USER>/.m2/repository/io/opentracing/opentracing-noop/0.33.0/opentracing-noop-0.33.0.jar"]}, crypto-random/crypto-random {:mvn/version "1.2.1", :exclusions #{commons-code/commons-codec}, :deps/manifest :mvn, :dependents [io.pedestal/pedestal.service], :parents #{[io.pedestal/pedestal.service]}, :paths ["/home/<USER>/.m2/repository/crypto-random/crypto-random/1.2.1/crypto-random-1.2.1.jar"]}, ring/ring-codec {:mvn/version "1.2.0", :deps/manifest :mvn, :dependents [ring/ring-core], :parents #{[io.pedestal/pedestal.service ring/ring-core]}, :paths ["/home/<USER>/.m2/repository/ring/ring-codec/1.2.0/ring-codec-1.2.0.jar"]}, org.apache.httpcomponents/httpclient {:mvn/version "4.5.13", :deps/manifest :mvn, :dependents [clj-http/clj-http org.apache.httpcomponents/httpmime org.apache.httpcomponents/httpclient-cache], :parents #{[clj-http/clj-http] [clj-http/clj-http org.apache.httpcomponents/httpmime] [clj-http/clj-http org.apache.httpcomponents/httpclient-cache]}, :paths ["/home/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.13/httpclient-4.5.13.jar"]}, prismatic/schema {:mvn/version "1.4.1", :deps/manifest :mvn, :parents #{[]}, :paths ["/home/<USER>/.m2/repository/prismatic/schema/1.4.1/schema-1.4.1.jar"]}, crypto-equality/crypto-equality {:mvn/version "1.0.1", :deps/manifest :mvn, :dependents [io.pedestal/pedestal.service ring/ring-core], :parents #{[io.pedestal/pedestal.service] [io.pedestal/pedestal.service ring/ring-core]}, :paths ["/home/<USER>/.m2/repository/crypto-equality/crypto-equality/1.0.1/crypto-equality-1.0.1.jar"]}, cheshire/cheshire {:mvn/version "5.11.0", :deps/manifest :mvn, :dependents [io.pedestal/pedestal.service], :parents #{[io.pedestal/pedestal.service]}, :paths ["/home/<USER>/.m2/repository/cheshire/cheshire/5.11.0/cheshire-5.11.0.jar"]}, tigris/tigris {:mvn/version "0.1.2", :deps/manifest :mvn, :dependents [cheshire/cheshire], :parents #{[io.pedestal/pedestal.service cheshire/cheshire]}, :paths ["/home/<USER>/.m2/repository/tigris/tigris/0.1.2/tigris-0.1.2.jar"]}, org.eclipse.jetty/jetty-client {:mvn/version "9.4.53.v20231009", :deps/manifest :mvn, :dependents [org.eclipse.jetty.websocket/websocket-client], :parents #{[io.pedestal/pedestal.jetty org.eclipse.jetty.websocket/websocket-server org.eclipse.jetty.websocket/websocket-client]}, :paths ["/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-client/9.4.53.v20231009/jetty-client-9.4.53.v20231009.jar"]}, org.clojure/core.match {:mvn/version "1.0.1", :exclusions #{org.clojure/clojurescript}, :deps/manifest :mvn, :dependents [io.pedestal/pedestal.interceptor], :parents #{[io.pedestal/pedestal.service io.pedestal/pedestal.interceptor]}, :paths ["/home/<USER>/.m2/repository/org/clojure/core.match/1.0.1/core.match-1.0.1.jar"]}, org.eclipse.jetty/jetty-io {:mvn/version "9.4.53.v20231009", :deps/manifest :mvn, :dependents [org.eclipse.jetty.http2/http2-hpack org.eclipse.jetty/jetty-server org.eclipse.jetty.websocket/websocket-client org.eclipse.jetty/jetty-client org.eclipse.jetty.websocket/websocket-common org.eclipse.jetty/jetty-http], :parents #{[io.pedestal/pedestal.jetty org.eclipse.jetty.http2/http2-server org.eclipse.jetty.http2/http2-common org.eclipse.jetty.http2/http2-hpack] [io.pedestal/pedestal.jetty org.eclipse.jetty/jetty-server] [io.pedestal/pedestal.jetty org.eclipse.jetty.websocket/websocket-server org.eclipse.jetty.websocket/websocket-client] [io.pedestal/pedestal.jetty org.eclipse.jetty.websocket/websocket-server org.eclipse.jetty.websocket/websocket-client org.eclipse.jetty/jetty-client] [io.pedestal/pedestal.jetty org.eclipse.jetty.websocket/websocket-server org.eclipse.jetty.websocket/websocket-common] [io.pedestal/pedestal.jetty org.eclipse.jetty.websocket/websocket-server org.eclipse.jetty/jetty-http]}, :paths ["/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-io/9.4.53.v20231009/jetty-io-9.4.53.v20231009.jar"]}, org.clojure/tools.reader {:mvn/version "1.3.6", :deps/manifest :mvn, :dependents [io.pedestal/pedestal.service org.clojure/tools.analyzer.jvm], :parents #{[io.pedestal/pedestal.service] [io.pedestal/pedestal.service org.clojure/core.async org.clojure/tools.analyzer.jvm]}, :paths ["/home/<USER>/.m2/repository/org/clojure/tools.reader/1.3.6/tools.reader-1.3.6.jar"]}, io.opentracing/opentracing-util {:mvn/version "0.33.0", :deps/manifest :mvn, :dependents [io.pedestal/pedestal.log], :parents #{[io.pedestal/pedestal.jetty io.pedestal/pedestal.log]}, :paths ["/home/<USER>/.m2/repository/io/opentracing/opentracing-util/0.33.0/opentracing-util-0.33.0.jar"]}, javax.servlet/javax.servlet-api {:mvn/version "3.1.0", :deps/manifest :mvn, :dependents [org.eclipse.jetty/jetty-server org.eclipse.jetty.websocket/websocket-servlet io.pedestal/pedestal.jetty], :parents #{[io.pedestal/pedestal.jetty org.eclipse.jetty/jetty-server] [io.pedestal/pedestal.jetty org.eclipse.jetty.websocket/websocket-servlet] [io.pedestal/pedestal.jetty]}, :paths ["/home/<USER>/.m2/repository/javax/servlet/javax.servlet-api/3.1.0/javax.servlet-api-3.1.0.jar"]}, potemkin/potemkin {:mvn/version "0.4.5", :exclusions #{org.clojure/clojure}, :deps/manifest :mvn, :dependents [clj-http/clj-http], :parents #{[clj-http/clj-http]}, :paths ["/home/<USER>/.m2/repository/potemkin/potemkin/0.4.5/potemkin-0.4.5.jar"]}, org.xerial/sqlite-jdbc {:mvn/version "********", :deps/manifest :mvn, :parents #{[]}, :paths ["/home/<USER>/.m2/repository/org/xerial/sqlite-jdbc/********/sqlite-jdbc-********.jar"]}, org.slf4j/slf4j-api {:mvn/version "1.7.36", :deps/manifest :mvn, :dependents [org.xerial/sqlite-jdbc io.dropwizard.metrics/metrics-core io.dropwizard.metrics/metrics-jmx], :parents #{[org.xerial/sqlite-jdbc] [io.pedestal/pedestal.jetty io.pedestal/pedestal.log io.dropwizard.metrics/metrics-core] [io.pedestal/pedestal.jetty io.pedestal/pedestal.log io.dropwizard.metrics/metrics-jmx]}, :paths ["/home/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar"]}, org.eclipse.jetty.websocket/websocket-server {:mvn/version "9.4.53.v20231009", :deps/manifest :mvn, :dependents [io.pedestal/pedestal.jetty], :parents #{[io.pedestal/pedestal.jetty]}, :paths ["/home/<USER>/.m2/repository/org/eclipse/jetty/websocket/websocket-server/9.4.53.v20231009/websocket-server-9.4.53.v20231009.jar"]}, io.dropwizard.metrics/metrics-jmx {:mvn/version "4.2.18", :deps/manifest :mvn, :dependents [io.pedestal/pedestal.log], :parents #{[io.pedestal/pedestal.jetty io.pedestal/pedestal.log]}, :paths ["/home/<USER>/.m2/repository/io/dropwizard/metrics/metrics-jmx/4.2.18/metrics-jmx-4.2.18.jar"]}, org.eclipse.jetty.websocket/websocket-common {:mvn/version "9.4.53.v20231009", :deps/manifest :mvn, :dependents [org.eclipse.jetty.websocket/websocket-client org.eclipse.jetty.websocket/websocket-server], :parents #{[io.pedestal/pedestal.jetty org.eclipse.jetty.websocket/websocket-server org.eclipse.jetty.websocket/websocket-client] [io.pedestal/pedestal.jetty org.eclipse.jetty.websocket/websocket-server]}, :paths ["/home/<USER>/.m2/repository/org/eclipse/jetty/websocket/websocket-common/9.4.53.v20231009/websocket-common-9.4.53.v20231009.jar"]}, io.pedestal/pedestal.route {:mvn/version "0.6.3", :deps/manifest :mvn, :dependents [io.pedestal/pedestal.service], :parents #{[io.pedestal/pedestal.service]}, :paths ["/home/<USER>/.m2/repository/io/pedestal/pedestal.route/0.6.3/pedestal.route-0.6.3.jar"]}, org.clojure/core.memoize {:mvn/version "1.0.253", :deps/manifest :mvn, :dependents [org.clojure/tools.analyzer.jvm], :parents #{[io.pedestal/pedestal.service org.clojure/core.async org.clojure/tools.analyzer.jvm]}, :paths ["/home/<USER>/.m2/repository/org/clojure/core.memoize/1.0.253/core.memoize-1.0.253.jar"]}, camel-snake-kebab/camel-snake-kebab {:mvn/version "0.4.3", :deps/manifest :mvn, :dependents [com.github.seancorfield/next.jdbc], :parents #{[com.github.seancorfield/next.jdbc]}, :paths ["/home/<USER>/.m2/repository/camel-snake-kebab/camel-snake-kebab/0.4.3/camel-snake-kebab-0.4.3.jar"]}, org.clojure/data.priority-map {:mvn/version "1.1.0", :deps/manifest :mvn, :dependents [org.clojure/core.cache], :parents #{[io.pedestal/pedestal.service org.clojure/core.async org.clojure/tools.analyzer.jvm org.clojure/core.memoize org.clojure/core.cache]}, :paths ["/home/<USER>/.m2/repository/org/clojure/data.priority-map/1.1.0/data.priority-map-1.1.0.jar"]}, org.clojure/java.data {:mvn/version "1.1.103", :deps/manifest :mvn, :dependents [com.github.seancorfield/next.jdbc], :parents #{[com.github.seancorfield/next.jdbc]}, :paths ["/home/<USER>/.m2/repository/org/clojure/java.data/1.1.103/java.data-1.1.103.jar"]}, org.eclipse.jetty/jetty-server {:mvn/version "9.4.53.v20231009", :deps/manifest :mvn, :dependents [org.eclipse.jetty/jetty-alpn-server org.eclipse.jetty.http2/http2-server org.eclipse.jetty/jetty-security io.pedestal/pedestal.jetty], :parents #{[io.pedestal/pedestal.jetty org.eclipse.jetty/jetty-alpn-server] [io.pedestal/pedestal.jetty org.eclipse.jetty.http2/http2-server] [io.pedestal/pedestal.jetty org.eclipse.jetty/jetty-servlet org.eclipse.jetty/jetty-security] [io.pedestal/pedestal.jetty]}, :paths ["/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-server/9.4.53.v20231009/jetty-server-9.4.53.v20231009.jar"]}, org.apache.httpcomponents/httpmime {:mvn/version "4.5.13", :exclusions #{org.clojure/clojure}, :deps/manifest :mvn, :dependents [clj-http/clj-http], :parents #{[clj-http/clj-http]}, :paths ["/home/<USER>/.m2/repository/org/apache/httpcomponents/httpmime/4.5.13/httpmime-4.5.13.jar"]}, ring/ring-core {:mvn/version "1.10.0", :exclusions #{org.clojure/clojure crypto-random/crypto-equality crypto-random/crypto-random org.clojure/tools.reader}, :deps/manifest :mvn, :dependents [io.pedestal/pedestal.service], :parents #{[io.pedestal/pedestal.service]}, :paths ["/home/<USER>/.m2/repository/ring/ring-core/1.10.0/ring-core-1.10.0.jar"]}, org.clojure/core.cache {:mvn/version "1.0.225", :deps/manifest :mvn, :dependents [org.clojure/core.memoize], :parents #{[io.pedestal/pedestal.service org.clojure/core.async org.clojure/tools.analyzer.jvm org.clojure/core.memoize]}, :paths ["/home/<USER>/.m2/repository/org/clojure/core.cache/1.0.225/core.cache-1.0.225.jar"]}, org.clojure/core.async {:mvn/version "1.6.673", :deps/manifest :mvn, :dependents [io.pedestal/pedestal.service io.pedestal/pedestal.interceptor io.pedestal/pedestal.route], :parents #{[io.pedestal/pedestal.service] [io.pedestal/pedestal.service io.pedestal/pedestal.interceptor] [io.pedestal/pedestal.service io.pedestal/pedestal.route]}, :paths ["/home/<USER>/.m2/repository/org/clojure/core.async/1.6.673/core.async-1.6.673.jar"]}, com.fasterxml.jackson.dataformat/jackson-dataformat-smile {:mvn/version "2.13.3", :exclusions #{com.fasterxml.jackson.core/jackson-databind}, :deps/manifest :mvn, :dependents [cheshire/cheshire], :parents #{[io.pedestal/pedestal.service cheshire/cheshire]}, :paths ["/home/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-smile/2.13.3/jackson-dataformat-smile-2.13.3.jar"]}, javax.xml.bind/jaxb-api {:mvn/version "2.3.0", :deps/manifest :mvn, :dependents [com.cognitect/transit-java], :parents #{[io.pedestal/pedestal.service com.cognitect/transit-clj com.cognitect/transit-java]}, :paths ["/home/<USER>/.m2/repository/javax/xml/bind/jaxb-api/2.3.0/jaxb-api-2.3.0.jar"]}, org.eclipse.jetty/jetty-alpn-server {:mvn/version "9.4.53.v20231009", :deps/manifest :mvn, :dependents [io.pedestal/pedestal.jetty], :parents #{[io.pedestal/pedestal.jetty]}, :paths ["/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-alpn-server/9.4.53.v20231009/jetty-alpn-server-9.4.53.v20231009.jar"]}}, :classpath-roots ["src" "resources" "/home/<USER>/.m2/repository/clj-http/clj-http/3.12.3/clj-http-3.12.3.jar" "/home/<USER>/.m2/repository/clojure/java-time/clojure.java-time/1.4.2/clojure.java-time-1.4.2.jar" "/home/<USER>/.m2/repository/com/github/seancorfield/next.jdbc/1.3.909/next.jdbc-1.3.909.jar" "/home/<USER>/.m2/repository/io/pedestal/pedestal.jetty/0.6.3/pedestal.jetty-0.6.3.jar" "/home/<USER>/.m2/repository/io/pedestal/pedestal.service/0.6.3/pedestal.service-0.6.3.jar" "/home/<USER>/.m2/repository/org/clojure/clojure/1.11.1/clojure-1.11.1.jar" "/home/<USER>/.m2/repository/org/clojure/data.json/2.4.0/data.json-2.4.0.jar" "/home/<USER>/.m2/repository/org/clojure/tools.logging/1.2.4/tools.logging-1.2.4.jar" "/home/<USER>/.m2/repository/org/xerial/sqlite-jdbc/********/sqlite-jdbc-********.jar" "/home/<USER>/.m2/repository/prismatic/schema/1.4.1/schema-1.4.1.jar" "/home/<USER>/.m2/repository/commons-codec/commons-codec/1.15/commons-codec-1.15.jar" "/home/<USER>/.m2/repository/org/apache/httpcomponents/httpasyncclient/4.1.4/httpasyncclient-4.1.4.jar" "/home/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.13/httpclient-4.5.13.jar" "/home/<USER>/.m2/repository/org/apache/httpcomponents/httpclient-cache/4.5.13/httpclient-cache-4.5.13.jar" "/home/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.14/httpcore-4.4.14.jar" "/home/<USER>/.m2/repository/org/apache/httpcomponents/httpmime/4.5.13/httpmime-4.5.13.jar" "/home/<USER>/.m2/repository/potemkin/potemkin/0.4.5/potemkin-0.4.5.jar" "/home/<USER>/.m2/repository/slingshot/slingshot/0.12.2/slingshot-0.12.2.jar" "/home/<USER>/.m2/repository/camel-snake-kebab/camel-snake-kebab/0.4.3/camel-snake-kebab-0.4.3.jar" "/home/<USER>/.m2/repository/org/clojure/java.data/1.1.103/java.data-1.1.103.jar" "/home/<USER>/.m2/repository/io/pedestal/pedestal.log/0.6.3/pedestal.log-0.6.3.jar" "/home/<USER>/.m2/repository/javax/servlet/javax.servlet-api/3.1.0/javax.servlet-api-3.1.0.jar" "/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-alpn-server/9.4.53.v20231009/jetty-alpn-server-9.4.53.v20231009.jar" "/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-server/9.4.53.v20231009/jetty-server-9.4.53.v20231009.jar" "/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-servlet/9.4.53.v20231009/jetty-servlet-9.4.53.v20231009.jar" "/home/<USER>/.m2/repository/org/eclipse/jetty/alpn/alpn-api/1.1.3.v20160715/alpn-api-1.1.3.v20160715.jar" "/home/<USER>/.m2/repository/org/eclipse/jetty/http2/http2-server/9.4.53.v20231009/http2-server-9.4.53.v20231009.jar" "/home/<USER>/.m2/repository/org/eclipse/jetty/websocket/websocket-api/9.4.53.v20231009/websocket-api-9.4.53.v20231009.jar" "/home/<USER>/.m2/repository/org/eclipse/jetty/websocket/websocket-server/9.4.53.v20231009/websocket-server-9.4.53.v20231009.jar" "/home/<USER>/.m2/repository/org/eclipse/jetty/websocket/websocket-servlet/9.4.53.v20231009/websocket-servlet-9.4.53.v20231009.jar" "/home/<USER>/.m2/repository/cheshire/cheshire/5.11.0/cheshire-5.11.0.jar" "/home/<USER>/.m2/repository/com/cognitect/transit-clj/1.0.329/transit-clj-1.0.329.jar" "/home/<USER>/.m2/repository/crypto-equality/crypto-equality/1.0.1/crypto-equality-1.0.1.jar" "/home/<USER>/.m2/repository/crypto-random/crypto-random/1.2.1/crypto-random-1.2.1.jar" "/home/<USER>/.m2/repository/io/pedestal/pedestal.interceptor/0.6.3/pedestal.interceptor-0.6.3.jar" "/home/<USER>/.m2/repository/io/pedestal/pedestal.route/0.6.3/pedestal.route-0.6.3.jar" "/home/<USER>/.m2/repository/org/clojure/core.async/1.6.673/core.async-1.6.673.jar" "/home/<USER>/.m2/repository/org/clojure/tools.reader/1.3.6/tools.reader-1.3.6.jar" "/home/<USER>/.m2/repository/ring/ring-core/1.10.0/ring-core-1.10.0.jar" "/home/<USER>/.m2/repository/org/clojure/core.specs.alpha/0.2.62/core.specs.alpha-0.2.62.jar" "/home/<USER>/.m2/repository/org/clojure/spec.alpha/0.3.218/spec.alpha-0.3.218.jar" "/home/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar" "/home/<USER>/.m2/repository/commons-logging/commons-logging/1.2/commons-logging-1.2.jar" "/home/<USER>/.m2/repository/org/apache/httpcomponents/httpcore-nio/4.4.10/httpcore-nio-4.4.10.jar" "/home/<USER>/.m2/repository/clj-tuple/clj-tuple/0.2.2/clj-tuple-0.2.2.jar" "/home/<USER>/.m2/repository/riddley/riddley/0.1.12/riddley-0.1.12.jar" "/home/<USER>/.m2/repository/io/dropwizard/metrics/metrics-core/4.2.18/metrics-core-4.2.18.jar" "/home/<USER>/.m2/repository/io/dropwizard/metrics/metrics-jmx/4.2.18/metrics-jmx-4.2.18.jar" "/home/<USER>/.m2/repository/io/opentracing/opentracing-api/0.33.0/opentracing-api-0.33.0.jar" "/home/<USER>/.m2/repository/io/opentracing/opentracing-util/0.33.0/opentracing-util-0.33.0.jar" "/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-http/9.4.53.v20231009/jetty-http-9.4.53.v20231009.jar" "/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-io/9.4.53.v20231009/jetty-io-9.4.53.v20231009.jar" "/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-security/9.4.53.v20231009/jetty-security-9.4.53.v20231009.jar" "/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-util-ajax/9.4.53.v20231009/jetty-util-ajax-9.4.53.v20231009.jar" "/home/<USER>/.m2/repository/org/eclipse/jetty/http2/http2-common/9.4.53.v20231009/http2-common-9.4.53.v20231009.jar" "/home/<USER>/.m2/repository/org/eclipse/jetty/websocket/websocket-client/9.4.53.v20231009/websocket-client-9.4.53.v20231009.jar" "/home/<USER>/.m2/repository/org/eclipse/jetty/websocket/websocket-common/9.4.53.v20231009/websocket-common-9.4.53.v20231009.jar" "/home/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.13.3/jackson-core-2.13.3.jar" "/home/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-cbor/2.13.3/jackson-dataformat-cbor-2.13.3.jar" "/home/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-smile/2.13.3/jackson-dataformat-smile-2.13.3.jar" "/home/<USER>/.m2/repository/tigris/tigris/0.1.2/tigris-0.1.2.jar" "/home/<USER>/.m2/repository/com/cognitect/transit-java/1.0.362/transit-java-1.0.362.jar" "/home/<USER>/.m2/repository/org/clojure/core.match/1.0.1/core.match-1.0.1.jar" "/home/<USER>/.m2/repository/org/clojure/tools.analyzer.jvm/1.2.2/tools.analyzer.jvm-1.2.2.jar" "/home/<USER>/.m2/repository/commons-fileupload/commons-fileupload/1.5/commons-fileupload-1.5.jar" "/home/<USER>/.m2/repository/commons-io/commons-io/2.11.0/commons-io-2.11.0.jar" "/home/<USER>/.m2/repository/ring/ring-codec/1.2.0/ring-codec-1.2.0.jar" "/home/<USER>/.m2/repository/io/opentracing/opentracing-noop/0.33.0/opentracing-noop-0.33.0.jar" "/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-util/9.4.53.v20231009/jetty-util-9.4.53.v20231009.jar" "/home/<USER>/.m2/repository/org/eclipse/jetty/http2/http2-hpack/9.4.53.v20231009/http2-hpack-9.4.53.v20231009.jar" "/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-client/9.4.53.v20231009/jetty-client-9.4.53.v20231009.jar" "/home/<USER>/.m2/repository/javax/xml/bind/jaxb-api/2.3.0/jaxb-api-2.3.0.jar" "/home/<USER>/.m2/repository/org/msgpack/msgpack/0.6.12/msgpack-0.6.12.jar" "/home/<USER>/.m2/repository/org/clojure/core.memoize/1.0.253/core.memoize-1.0.253.jar" "/home/<USER>/.m2/repository/org/clojure/tools.analyzer/1.1.0/tools.analyzer-1.1.0.jar" "/home/<USER>/.m2/repository/org/ow2/asm/asm/9.2/asm-9.2.jar" "/home/<USER>/.m2/repository/com/googlecode/json-simple/json-simple/1.1.1/json-simple-1.1.1.jar" "/home/<USER>/.m2/repository/org/javassist/javassist/3.18.1-GA/javassist-3.18.1-GA.jar" "/home/<USER>/.m2/repository/org/clojure/core.cache/1.0.225/core.cache-1.0.225.jar" "/home/<USER>/.m2/repository/org/clojure/data.priority-map/1.1.0/data.priority-map-1.1.0.jar"], :classpath {"/home/<USER>/.m2/repository/org/clojure/tools.analyzer.jvm/1.2.2/tools.analyzer.jvm-1.2.2.jar" {:lib-name org.clojure/tools.analyzer.jvm}, "/home/<USER>/.m2/repository/clj-tuple/clj-tuple/0.2.2/clj-tuple-0.2.2.jar" {:lib-name clj-tuple/clj-tuple}, "/home/<USER>/.m2/repository/org/eclipse/jetty/websocket/websocket-server/9.4.53.v20231009/websocket-server-9.4.53.v20231009.jar" {:lib-name org.eclipse.jetty.websocket/websocket-server}, "/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-util/9.4.53.v20231009/jetty-util-9.4.53.v20231009.jar" {:lib-name org.eclipse.jetty/jetty-util}, "/home/<USER>/.m2/repository/org/eclipse/jetty/websocket/websocket-client/9.4.53.v20231009/websocket-client-9.4.53.v20231009.jar" {:lib-name org.eclipse.jetty.websocket/websocket-client}, "/home/<USER>/.m2/repository/org/clojure/tools.logging/1.2.4/tools.logging-1.2.4.jar" {:lib-name org.clojure/tools.logging}, "/home/<USER>/.m2/repository/clojure/java-time/clojure.java-time/1.4.2/clojure.java-time-1.4.2.jar" {:lib-name clojure.java-time/clojure.java-time}, "/home/<USER>/.m2/repository/com/cognitect/transit-java/1.0.362/transit-java-1.0.362.jar" {:lib-name com.cognitect/transit-java}, "/home/<USER>/.m2/repository/commons-fileupload/commons-fileupload/1.5/commons-fileupload-1.5.jar" {:lib-name commons-fileupload/commons-fileupload}, "/home/<USER>/.m2/repository/io/opentracing/opentracing-util/0.33.0/opentracing-util-0.33.0.jar" {:lib-name io.opentracing/opentracing-util}, "/home/<USER>/.m2/repository/prismatic/schema/1.4.1/schema-1.4.1.jar" {:lib-name prismatic/schema}, "/home/<USER>/.m2/repository/io/dropwizard/metrics/metrics-core/4.2.18/metrics-core-4.2.18.jar" {:lib-name io.dropwizard.metrics/metrics-core}, "/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-http/9.4.53.v20231009/jetty-http-9.4.53.v20231009.jar" {:lib-name org.eclipse.jetty/jetty-http}, "/home/<USER>/.m2/repository/camel-snake-kebab/camel-snake-kebab/0.4.3/camel-snake-kebab-0.4.3.jar" {:lib-name camel-snake-kebab/camel-snake-kebab}, "/home/<USER>/.m2/repository/ring/ring-core/1.10.0/ring-core-1.10.0.jar" {:lib-name ring/ring-core}, "/home/<USER>/.m2/repository/org/clojure/tools.analyzer/1.1.0/tools.analyzer-1.1.0.jar" {:lib-name org.clojure/tools.analyzer}, "/home/<USER>/.m2/repository/io/opentracing/opentracing-noop/0.33.0/opentracing-noop-0.33.0.jar" {:lib-name io.opentracing/opentracing-noop}, "/home/<USER>/.m2/repository/org/clojure/tools.reader/1.3.6/tools.reader-1.3.6.jar" {:lib-name org.clojure/tools.reader}, "/home/<USER>/.m2/repository/com/cognitect/transit-clj/1.0.329/transit-clj-1.0.329.jar" {:lib-name com.cognitect/transit-clj}, "/home/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.13/httpclient-4.5.13.jar" {:lib-name org.apache.httpcomponents/httpclient}, "/home/<USER>/.m2/repository/org/eclipse/jetty/websocket/websocket-common/9.4.53.v20231009/websocket-common-9.4.53.v20231009.jar" {:lib-name org.eclipse.jetty.websocket/websocket-common}, "/home/<USER>/.m2/repository/io/pedestal/pedestal.log/0.6.3/pedestal.log-0.6.3.jar" {:lib-name io.pedestal/pedestal.log}, "/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-alpn-server/9.4.53.v20231009/jetty-alpn-server-9.4.53.v20231009.jar" {:lib-name org.eclipse.jetty/jetty-alpn-server}, "/home/<USER>/.m2/repository/org/msgpack/msgpack/0.6.12/msgpack-0.6.12.jar" {:lib-name org.msgpack/msgpack}, "/home/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-cbor/2.13.3/jackson-dataformat-cbor-2.13.3.jar" {:lib-name com.fasterxml.jackson.dataformat/jackson-dataformat-cbor}, "/home/<USER>/.m2/repository/org/clojure/spec.alpha/0.3.218/spec.alpha-0.3.218.jar" {:lib-name org.clojure/spec.alpha}, "/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-security/9.4.53.v20231009/jetty-security-9.4.53.v20231009.jar" {:lib-name org.eclipse.jetty/jetty-security}, "/home/<USER>/.m2/repository/org/apache/httpcomponents/httpasyncclient/4.1.4/httpasyncclient-4.1.4.jar" {:lib-name org.apache.httpcomponents/httpasyncclient}, "/home/<USER>/.m2/repository/org/ow2/asm/asm/9.2/asm-9.2.jar" {:lib-name org.ow2.asm/asm}, "/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-server/9.4.53.v20231009/jetty-server-9.4.53.v20231009.jar" {:lib-name org.eclipse.jetty/jetty-server}, "/home/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.13.3/jackson-core-2.13.3.jar" {:lib-name com.fasterxml.jackson.core/jackson-core}, "/home/<USER>/.m2/repository/org/clojure/core.async/1.6.673/core.async-1.6.673.jar" {:lib-name org.clojure/core.async}, "/home/<USER>/.m2/repository/javax/servlet/javax.servlet-api/3.1.0/javax.servlet-api-3.1.0.jar" {:lib-name javax.servlet/javax.servlet-api}, "/home/<USER>/.m2/repository/org/eclipse/jetty/alpn/alpn-api/1.1.3.v20160715/alpn-api-1.1.3.v20160715.jar" {:lib-name org.eclipse.jetty.alpn/alpn-api}, "src" {:path-key :paths}, "/home/<USER>/.m2/repository/org/clojure/data.priority-map/1.1.0/data.priority-map-1.1.0.jar" {:lib-name org.clojure/data.priority-map}, "/home/<USER>/.m2/repository/potemkin/potemkin/0.4.5/potemkin-0.4.5.jar" {:lib-name potemkin/potemkin}, "/home/<USER>/.m2/repository/com/googlecode/json-simple/json-simple/1.1.1/json-simple-1.1.1.jar" {:lib-name com.googlecode.json-simple/json-simple}, "/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-util-ajax/9.4.53.v20231009/jetty-util-ajax-9.4.53.v20231009.jar" {:lib-name org.eclipse.jetty/jetty-util-ajax}, "/home/<USER>/.m2/repository/org/clojure/java.data/1.1.103/java.data-1.1.103.jar" {:lib-name org.clojure/java.data}, "/home/<USER>/.m2/repository/commons-logging/commons-logging/1.2/commons-logging-1.2.jar" {:lib-name commons-logging/commons-logging}, "/home/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar" {:lib-name org.slf4j/slf4j-api}, "/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-servlet/9.4.53.v20231009/jetty-servlet-9.4.53.v20231009.jar" {:lib-name org.eclipse.jetty/jetty-servlet}, "/home/<USER>/.m2/repository/slingshot/slingshot/0.12.2/slingshot-0.12.2.jar" {:lib-name slingshot/slingshot}, "/home/<USER>/.m2/repository/org/apache/httpcomponents/httpclient-cache/4.5.13/httpclient-cache-4.5.13.jar" {:lib-name org.apache.httpcomponents/httpclient-cache}, "/home/<USER>/.m2/repository/ring/ring-codec/1.2.0/ring-codec-1.2.0.jar" {:lib-name ring/ring-codec}, "/home/<USER>/.m2/repository/org/clojure/core.cache/1.0.225/core.cache-1.0.225.jar" {:lib-name org.clojure/core.cache}, "/home/<USER>/.m2/repository/crypto-equality/crypto-equality/1.0.1/crypto-equality-1.0.1.jar" {:lib-name crypto-equality/crypto-equality}, "/home/<USER>/.m2/repository/org/xerial/sqlite-jdbc/********/sqlite-jdbc-********.jar" {:lib-name org.xerial/sqlite-jdbc}, "/home/<USER>/.m2/repository/io/opentracing/opentracing-api/0.33.0/opentracing-api-0.33.0.jar" {:lib-name io.opentracing/opentracing-api}, "/home/<USER>/.m2/repository/riddley/riddley/0.1.12/riddley-0.1.12.jar" {:lib-name riddley/riddley}, "/home/<USER>/.m2/repository/cheshire/cheshire/5.11.0/cheshire-5.11.0.jar" {:lib-name cheshire/cheshire}, "/home/<USER>/.m2/repository/org/apache/httpcomponents/httpmime/4.5.13/httpmime-4.5.13.jar" {:lib-name org.apache.httpcomponents/httpmime}, "/home/<USER>/.m2/repository/io/pedestal/pedestal.interceptor/0.6.3/pedestal.interceptor-0.6.3.jar" {:lib-name io.pedestal/pedestal.interceptor}, "/home/<USER>/.m2/repository/io/dropwizard/metrics/metrics-jmx/4.2.18/metrics-jmx-4.2.18.jar" {:lib-name io.dropwizard.metrics/metrics-jmx}, "/home/<USER>/.m2/repository/org/javassist/javassist/3.18.1-GA/javassist-3.18.1-GA.jar" {:lib-name org.javassist/javassist}, "/home/<USER>/.m2/repository/org/eclipse/jetty/websocket/websocket-api/9.4.53.v20231009/websocket-api-9.4.53.v20231009.jar" {:lib-name org.eclipse.jetty.websocket/websocket-api}, "/home/<USER>/.m2/repository/org/eclipse/jetty/websocket/websocket-servlet/9.4.53.v20231009/websocket-servlet-9.4.53.v20231009.jar" {:lib-name org.eclipse.jetty.websocket/websocket-servlet}, "/home/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-smile/2.13.3/jackson-dataformat-smile-2.13.3.jar" {:lib-name com.fasterxml.jackson.dataformat/jackson-dataformat-smile}, "/home/<USER>/.m2/repository/tigris/tigris/0.1.2/tigris-0.1.2.jar" {:lib-name tigris/tigris}, "/home/<USER>/.m2/repository/org/eclipse/jetty/http2/http2-server/9.4.53.v20231009/http2-server-9.4.53.v20231009.jar" {:lib-name org.eclipse.jetty.http2/http2-server}, "/home/<USER>/.m2/repository/io/pedestal/pedestal.service/0.6.3/pedestal.service-0.6.3.jar" {:lib-name io.pedestal/pedestal.service}, "/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-io/9.4.53.v20231009/jetty-io-9.4.53.v20231009.jar" {:lib-name org.eclipse.jetty/jetty-io}, "/home/<USER>/.m2/repository/org/clojure/data.json/2.4.0/data.json-2.4.0.jar" {:lib-name org.clojure/data.json}, "/home/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.14/httpcore-4.4.14.jar" {:lib-name org.apache.httpcomponents/httpcore}, "/home/<USER>/.m2/repository/com/github/seancorfield/next.jdbc/1.3.909/next.jdbc-1.3.909.jar" {:lib-name com.github.seancorfield/next.jdbc}, "/home/<USER>/.m2/repository/org/clojure/core.specs.alpha/0.2.62/core.specs.alpha-0.2.62.jar" {:lib-name org.clojure/core.specs.alpha}, "/home/<USER>/.m2/repository/org/clojure/core.match/1.0.1/core.match-1.0.1.jar" {:lib-name org.clojure/core.match}, "/home/<USER>/.m2/repository/org/clojure/clojure/1.11.1/clojure-1.11.1.jar" {:lib-name org.clojure/clojure}, "/home/<USER>/.m2/repository/org/clojure/core.memoize/1.0.253/core.memoize-1.0.253.jar" {:lib-name org.clojure/core.memoize}, "/home/<USER>/.m2/repository/org/apache/httpcomponents/httpcore-nio/4.4.10/httpcore-nio-4.4.10.jar" {:lib-name org.apache.httpcomponents/httpcore-nio}, "/home/<USER>/.m2/repository/io/pedestal/pedestal.jetty/0.6.3/pedestal.jetty-0.6.3.jar" {:lib-name io.pedestal/pedestal.jetty}, "/home/<USER>/.m2/repository/io/pedestal/pedestal.route/0.6.3/pedestal.route-0.6.3.jar" {:lib-name io.pedestal/pedestal.route}, "/home/<USER>/.m2/repository/commons-codec/commons-codec/1.15/commons-codec-1.15.jar" {:lib-name commons-codec/commons-codec}, "/home/<USER>/.m2/repository/org/eclipse/jetty/http2/http2-hpack/9.4.53.v20231009/http2-hpack-9.4.53.v20231009.jar" {:lib-name org.eclipse.jetty.http2/http2-hpack}, "/home/<USER>/.m2/repository/commons-io/commons-io/2.11.0/commons-io-2.11.0.jar" {:lib-name commons-io/commons-io}, "/home/<USER>/.m2/repository/crypto-random/crypto-random/1.2.1/crypto-random-1.2.1.jar" {:lib-name crypto-random/crypto-random}, "resources" {:path-key :paths}, "/home/<USER>/.m2/repository/org/eclipse/jetty/http2/http2-common/9.4.53.v20231009/http2-common-9.4.53.v20231009.jar" {:lib-name org.eclipse.jetty.http2/http2-common}, "/home/<USER>/.m2/repository/clj-http/clj-http/3.12.3/clj-http-3.12.3.jar" {:lib-name clj-http/clj-http}, "/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-client/9.4.53.v20231009/jetty-client-9.4.53.v20231009.jar" {:lib-name org.eclipse.jetty/jetty-client}, "/home/<USER>/.m2/repository/javax/xml/bind/jaxb-api/2.3.0/jaxb-api-2.3.0.jar" {:lib-name javax.xml.bind/jaxb-api}}, :basis-config {}}